[isort]
default_section = FIRSTPARTY
ensure_newline_before_comments = True
force_grid_wrap = 0
include_trailing_comma = True
known_first_party = sdtools
known_third_party =
    imblearn
    numpy
    pandas
    pytorch-tabnet
    scipy
    sklearn
    ipywidgets
    torch
    torchaudio
    torchvision
    torch_xla
    tqdm
    xgboost

line_length = 119
lines_after_imports = 2
multi_line_output = 3
use_parentheses = True

[flake8]
ignore = E203, E501, E741, W503, W605
max-line-length = 119