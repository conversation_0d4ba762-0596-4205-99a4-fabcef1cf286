# pyright: reportMissingImports=false
import random
import string
from pathlib import Path

import pytest


@pytest.fixture(autouse=True)
def _seed_all():
    random.seed(1337)
    try:
        import numpy as np  # type: ignore
        np.random.seed(1337)
    except Exception:
        pass
    yield


@pytest.fixture
def tmp_text_file(tmp_path: Path):
    def _maker(name: str, content: str) -> Path:
        p = tmp_path / name
        p.write_text(content, encoding="utf-8")
        return p
    return _maker


@pytest.fixture
def tmp_jsonl_file(tmp_path: Path):
    def _maker(name: str, rows: list[dict]) -> Path:
        p = tmp_path / name
        with p.open("w", encoding="utf-8") as f:
            for row in rows:
                import json
                f.write(json.dumps(row, default=str) + "\n")
        return p
    return _maker


@pytest.fixture
def tmp_module_file(tmp_path: Path):
    def _maker(module_name: str, class_name: str) -> Path:
        # create a simple module that defines a class with given name
        code = f"class {class_name}:\n    pass\n"
        p = tmp_path / f"{module_name}.py"
        p.write_text(code, encoding="utf-8")
        return p
    return _maker


@pytest.fixture
def random_dirnames():
    def _maker(n: int = 2) -> list[str]:
        def _rand():
            return "".join(random.choice(string.ascii_lowercase) for _ in range(6))
        return [_rand() for _ in range(n)]
    return _maker