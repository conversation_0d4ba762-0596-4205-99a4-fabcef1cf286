# pyright: reportMissingImports=false
import types
from pathlib import Path
from importlib.metadata import PackageNotFoundError
import subprocess
import sys

import pytest

from app.core.glue.common.utils import runtime_tasks as rt


class DummyHandler:
    pass


def test_str_to_class_with_import_path(monkeypatch):
    # Create a dummy module dynamically and add to sys.modules
    mod = types.ModuleType("dummy_mod")
    class_name = "MyClass"
    setattr(mod, class_name, type(class_name, (), {}))
    sys.modules["dummy_mod"] = mod

    cls = rt.str_to_class(class_name=class_name, import_path="dummy_mod")
    assert cls.__name__ == class_name


def test_str_to_class_with_file_path(tmp_module_file):
    # Create a temp module file with given class and import via file_path
    module_path = tmp_module_file("temp_module", "Foo")
    cls = rt.str_to_class(class_name="Foo", file_path=str(module_path))
    assert cls.__name__ == "Foo"


def test_str_to_class_prefers_import_path(tmp_module_file, monkeypatch):
    # If both import_path and file_path provided, import_path takes precedence
    mod = types.ModuleType("prio_mod")
    setattr(mod, "Bar", type("Bar", (), {"marker": "import_path"}))
    sys.modules["prio_mod"] = mod

    module_path = tmp_module_file("prio_file", "Bar")  # would have marker absent
    cls = rt.str_to_class(class_name="Bar", import_path="prio_mod", file_path=str(module_path))
    assert getattr(cls, "marker", None) == "import_path"


def test_install_lib_if_missing_already_installed(monkeypatch):
    class MockDistribution:
        def __init__(self, name):
            self.name = name
            self.version = "1.0.0"

    def fake_distribution(name):
        return MockDistribution(name)

    monkeypatch.setattr(rt, "distribution", fake_distribution)
    installed = rt.install_lib_if_missing("somepkg")
    assert installed is True


def test_install_lib_if_missing_triggers_pip(monkeypatch):
    # Simulate package not installed
    def raise_not_found(name):
        raise PackageNotFoundError

    calls = {"count": 0, "args": None}

    def fake_check_call(args, stdout=None, stderr=None):
        calls["count"] += 1
        calls["args"] = args
        return 0

    monkeypatch.setattr(rt, "distribution", raise_not_found)
    monkeypatch.setattr(subprocess, "check_call", fake_check_call)

    installed = rt.install_lib_if_missing("somepkg")
    assert installed is False
    assert calls["count"] == 1
    assert args_startswith_pip(calls["args"])


def test_install_lib_if_missing_with_find_links(monkeypatch):
    def raise_not_found(name):
        raise PackageNotFoundError

    captured = {}

    def fake_check_call(args, stdout=None, stderr=None):
        captured["args"] = args
        return 0

    monkeypatch.setattr(rt, "distribution", raise_not_found)
    monkeypatch.setattr(subprocess, "check_call", fake_check_call)

    rt.install_lib_if_missing("otherpkg==0.1.2", find_links="https://example/simple")
    args = captured["args"]
    assert "-f" in args
    assert "https://example/simple" in args


def args_startswith_pip(args):
    # Example: [sys.executable, "-m", "pip", "install", "somepkg"]
    return (
        args[0] == sys.executable
        and args[1:4] == ["-m", "pip", "install"]
    )