from pathlib import Path
import logging

from app.core.glue.common.utils import logging as glue_logging
from app.core.glue.common.constants.str_literals import FileConstants


def test_set_logging_config_offline_creates_logfile_and_handler(tmp_path: Path, capsys):
    log_dir = tmp_path / "logs"
    glue_logging.set_logging_config(str(log_dir), mode="offline")

    logger = glue_logging.get_glue_logger("test_module")
    logger.info("hello-json-log")

    # logfile must be created by basicConfig
    logfile = log_dir / FileConstants.logfile_name
    assert logfile.exists(), "Expected logfile to be created by basicConfig"

    # emitted message must have been processed by handlers (console handler in offline mode)
    captured = capsys.readouterr()
    assert "hello-json-log" in captured.err or "hello-json-log" in captured.out, "Log not emitted through handler"

    # logger returned should be the same singleton and keep handlers attached
    logger2 = glue_logging.get_glue_logger("test_module")
    assert logger is logger2 or logger2.handlers, "Expected handlers to be present on logger instance"
def test_set_logging_config_online_attaches_rotating_handler(tmp_path: Path):
    log_dir = tmp_path / "logs"
    glue_logging.set_logging_config(str(log_dir), mode="online")

    logger = glue_logging.get_glue_logger("test_online")

    # Ensure a TimedRotatingFileHandler is attached
    from logging.handlers import TimedRotatingFileHandler  # local import to avoid global import churn
    rotating_handlers = [h for h in logger.handlers if isinstance(h, TimedRotatingFileHandler)]
    assert rotating_handlers, "Expected a TimedRotatingFileHandler in online mode"

    h = rotating_handlers[0]
    # Handler suffix should be configured for daily rotation
    assert getattr(h, "suffix", None) == "%Y%m%d", "Expected daily rotation suffix to be set"
    # Base filename should end with the configured logfile prefix
    assert str(getattr(h, "baseFilename", "")).endswith(FileConstants.logfile_prefix), \
        "TimedRotatingFileHandler base filename should use the configured logfile prefix"


def test_logger_handlers_available_for_multiple_modules(tmp_path: Path, capsys):
    log_dir = tmp_path / "logs"
    glue_logging.set_logging_config(str(log_dir), mode="offline")

    logger_a = glue_logging.get_glue_logger("moduleA")
    logger_b = glue_logging.get_glue_logger("moduleB")

    logger_a.info("alpha-log")
    logger_b.info("beta-log")

    # Both module loggers should emit through the configured handlers
    captured = capsys.readouterr()
    out = captured.out + captured.err
    assert "alpha-log" in out and "beta-log" in out, "Expected both module logs to be captured"