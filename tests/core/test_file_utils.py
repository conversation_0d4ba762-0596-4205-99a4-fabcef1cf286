# pyright: reportMissingImports=false
import json
from pathlib import Path

import pytest

from app.core.glue.common.utils import file as file_utils
from app.core.glue.common.exceptions import GlueValidaionException


def test_yaml_to_dict_success(tmp_path: Path):
    yaml_content = "a: 1\nb: test\n"
    f = tmp_path / "ex.yaml"
    f.write_text(yaml_content, encoding="utf-8")

    out = file_utils.yaml_to_dict(str(f))
    assert out == {"a": 1, "b": "test"}


def test_yaml_to_dict_invalid_yaml_raises(tmp_path: Path):
    yaml_content = "a: [1, 2\n"  # missing closing bracket
    f = tmp_path / "bad.yaml"
    f.write_text(yaml_content, encoding="utf-8")

    with pytest.raises(GlueValidaionException):
        file_utils.yaml_to_dict(str(f))


def test_yaml_to_class_with_defaults(tmp_path: Path):
    class Dummy:
        def __init__(self, a: int = 1, b: str = "x"):
            self.a = a
            self.b = b

    # default yaml
    default_f = tmp_path / "default.yaml"
    default_f.write_text("a: 1\nb: def\n", encoding="utf-8")

    # user yaml missing b
    user_f = tmp_path / "user.yaml"
    user_f.write_text("a: 2\n", encoding="utf-8")

    obj = file_utils.yaml_to_class(str(user_f), Dummy, str(default_f))
    assert isinstance(obj, Dummy)
    assert obj.a == 2
    assert obj.b == "def"


def test_yaml_to_class_type_error_raises(tmp_path: Path):
    class Dummy:
        def __init__(self, a: int):
            self.a = a

    user_f = tmp_path / "user.yaml"
    user_f.write_text("a: not_an_int\n", encoding="utf-8")

    with pytest.raises(GlueValidaionException):
        file_utils.yaml_to_class(str(user_f), Dummy)


def test_read_jsonl_and_row(tmp_path: Path):
    p = tmp_path / "x.jsonl"
    rows = [{"i": 0}, {"i": 1}, {"i": 2}]
    with p.open("w", encoding="utf-8") as f:
        for r in rows:
            f.write(json.dumps(r) + "\n")

    # read all
    all_rows = file_utils.read_jsonl(str(p))
    assert all_rows == rows

    # read generator
    gen_rows = list(file_utils.read_jsonl_row(str(p)))
    assert gen_rows == rows


def test_append_and_save_jsonl(tmp_path: Path):
    p = tmp_path / "a.jsonl"
    file_utils.append_as_jsonl(str(p), {"x": 1})
    file_utils.save_jsonlist(str(p), [{"y": 2}], mode="a")

    lines = p.read_text(encoding="utf-8").strip().splitlines()
    assert len(lines) == 2
    assert json.loads(lines[0]) == {"x": 1}
    assert json.loads(lines[1]) == {"y": 2}


def test_str_list_to_dir_path(random_dirnames):
    parts = random_dirnames(3)
    out = file_utils.str_list_to_dir_path(parts)
    # ensure last segment preserved, and uses OS separators implicitly
    assert out.endswith(parts[-1])
    for seg in parts:
        assert seg in out