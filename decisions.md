# Architectural Decision Records (ADRs)

This document logs significant architectural decisions made for the PromptWizard project. Each decision record should include:

- **ID:** Unique identifier (e.g., DEC-001)
- **Status:** Accepted, Superseded, or Rejected
- **Category:** Technical, Infrastructure, Security, etc.
- **Stakeholders:** Who was involved in the decision
- **Decision:** A clear statement of the decision
- **Context:** The problem or situation that led to the decision
- **Alternatives Considered:** Other options that were evaluated
- **Rationale:** Why this decision was chosen over alternatives
- **Consequences:** Positive and negative impacts of the decision

---

## DEC-001: Initial Project Structure and Tech Stack (2025-08-20)

**Status:** Accepted
**Category:** Technical
**Stakeholders:** AI Assistant, User

### Decision

The PromptWizard project will adopt a monorepo-like structure with a FastAPI backend in the `app/` directory and a React frontend (using Vite and Tailwind CSS) in the `src/` directory. Docker Compose will be used for local development and containerization.

### Context

The original PromptWizard project had a flat structure primarily focused on Python scripts and documentation. To evolve into a modern, user-friendly UI, a clear separation of concerns between backend API and frontend application is necessary. Adherence to the `.dev-rules/AGENTS.md` blueprint is mandatory.

### Alternatives Considered

1.  **Separate Repositories:** Considered for strict decoupling but rejected due to increased overhead in managing two distinct repositories and coordinating deployments for a single logical application.
2.  **Different Frontend Frameworks (e.g., Vue, Angular):** Rejected in favor of React due to its extensive ecosystem, community support, and alignment with the blueprint's preferred tech stack.
3.  **Different Backend Frameworks (e.g., Node.js, Django):** Rejected in favor of FastAPI due to its performance, modern Python features, and suitability for AI/ML integrations, as well as alignment with the blueprint.

### Rationale

This decision aligns with the `.dev-rules/AGENTS.md` blueprint's "High-Level Project Structure" and "Tech Stack" sections, promoting modularity, scalability, and maintainability. It provides a clear separation for development teams and leverages established, performant technologies. Docker Compose simplifies local setup and ensures consistency across development environments.

### Consequences

-   **Positive:** Clear separation of frontend and backend concerns, improved development workflow with Docker, adherence to architectural best practices, and a modern tech stack for future scalability.
-   **Negative:** Requires initial setup and configuration for both Python and Node.js environments, and introduces a multi-language development environment.