# FastAPI Backend Configuration
PROJECT_NAME="PromptWizard"
ENVIRONMENT="development" # development, staging, production
LOG_LEVEL="INFO" # DEBUG, INFO, WARNING, ERROR, CRITICAL
API_V1_STR="/api/v1"
SECRET_KEY="your_super_secret_key_here" # Change this in production
ACCESS_TOKEN_EXPIRE_MINUTES=30

# Database Configuration (PostgreSQL)
POSTGRES_SERVER="localhost"
POSTGRES_PORT="5432"
POSTGRES_DB="promptwizard_db"
POSTGRES_USER="promptwizard_user"
POSTGRES_PASSWORD="promptwizard_password"

# Redis Configuration
REDIS_HOST="localhost"
REDIS_PORT="6379"
REDIS_DB=0

# CORS Origins (comma-separated)
CORS_ORIGINS="http://localhost:5173,http://localhost:8000"

# Trusted Hosts (comma-separated)
TRUSTED_HOSTS="localhost,127.0.0.1"

# File Uploads
UPLOAD_DIR="./uploads"
TEMP_DIR="./temp"

# Frontend Configuration (Vite/React)
VITE_API_BASE_URL="http://localhost:8000/api/v1"