import React from 'react';
import { cn } from '../../lib/utils';

export function Progress({ 
  value = 0, 
  max = 100,
  className,
  showLabel = false,
  ...props 
}) {
  const percentage = Math.min(100, Math.max(0, (value / max) * 100));
  
  return (
    <div className={cn('relative', className)} {...props}>
      <div className="overflow-hidden h-2 text-xs flex rounded-full bg-gray-200">
        <div
          style={{ width: `${percentage}%` }}
          className="shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center bg-primary-600 transition-all duration-500 ease-out rounded-full"
        />
      </div>
      {showLabel && (
        <span className="absolute right-0 top-0 -mt-6 text-caption text-gray-600 font-medium">
          {Math.round(percentage)}%
        </span>
      )}
    </div>
  );
}