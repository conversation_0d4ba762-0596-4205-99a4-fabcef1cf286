import React from 'react';
import { cn } from '../../lib/utils';

const badgeVariants = {
  default: 'bg-gray-100 text-gray-700',
  primary: 'bg-primary-50 text-primary-600',
  success: 'bg-success-400/10 text-success-600',
  warning: 'bg-warning-400/10 text-warning-500',
  danger: 'bg-error-400/10 text-error-500',
  accent: 'bg-accent-400/10 text-accent-600'
};

export function Badge({ 
  variant = 'default', 
  className, 
  children, 
  ...props 
}) {
  return (
    <span
      className={cn(
        'inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-semibold',
        badgeVariants[variant],
        className
      )}
      {...props}
    >
      {children}
    </span>
  );
}