import React from 'react';
import { cn } from '../../lib/utils';

const buttonVariants = {
  default: 'bg-primary-600 text-white hover:bg-primary-700 focus-visible:ring-primary-500',
  secondary: 'bg-gray-100 text-gray-900 hover:bg-gray-200 focus-visible:ring-gray-500',
  accent: 'bg-accent-500 text-white hover:bg-accent-600 focus-visible:ring-accent-500',
  ghost: 'hover:bg-gray-100 text-gray-700 focus-visible:ring-gray-500',
  success: 'bg-success-500 text-white hover:bg-success-600 focus-visible:ring-success-500',
  warning: 'bg-warning-500 text-white hover:bg-warning-600 focus-visible:ring-warning-500',
  danger: 'bg-error-500 text-white hover:bg-error-600 focus-visible:ring-error-500'
};

export function Button({ 
  variant = 'default', 
  size = 'md', 
  className, 
  children,
  disabled = false,
  ...props 
}) {
  return (
    <button
      className={cn(
        'inline-flex items-center justify-center rounded-md font-medium transition-colors',
        'focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2',
        'disabled:pointer-events-none disabled:opacity-50',
        buttonVariants[variant],
        size === 'sm' && 'h-8 px-3 text-sm',
        size === 'md' && 'h-10 px-4 text-sm',
        size === 'lg' && 'h-12 px-6 text-base',
        className
      )}
      disabled={disabled}
      {...props}
    >
      {children}
    </button>
  );
}