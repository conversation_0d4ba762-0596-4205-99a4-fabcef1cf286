import React, { useState } from 'react';
import { Card, CardContent } from '../ui/Card';
import { Button } from '../ui/Button';
import { Input, Textarea } from '../ui/Input';
import { Progress } from '../ui/Progress';
import { Spinner } from '../ui/Spinner';
import { Badge } from '../ui/Badge';
import { useOptimizationStore } from '../../store/optimizationStore';

export function PromptOptimizer({ onNavigate }) {
  const [promptText, setPromptText] = useState('');
  const [promptTitle, setPromptTitle] = useState('');
  const [config, setConfig] = useState({
    technique: 'critique_n_refine',
    iterations: 3,
    batchSize: 5,
    successThreshold: 80
  });
  
  const { 
    startOptimization, 
    updateProgress,
    completeOptimization,
    isOptimizing, 
    currentOptimization 
  } = useOptimizationStore();
  
  const handleStartOptimization = async () => {
    if (!promptText || !promptTitle) return;
    
    // Start the optimization
    startOptimization({
      title: promptTitle,
      originalPrompt: promptText,
      config
    });
    
    // Simulate optimization process
    const stages = [
      { stage: 'initializing', progress: 10, duration: 1000 },
      { stage: 'generating', progress: 25, duration: 2000 },
      { stage: 'testing', progress: 45, duration: 2500 },
      { stage: 'critiquing', progress: 60, duration: 2000 },
      { stage: 'refining', progress: 80, duration: 2000 },
      { stage: 'evaluating', progress: 95, duration: 1500 },
      { stage: 'complete', progress: 100, duration: 500 }
    ];
    
    // Simulate progress through stages
    for (const { stage, progress, duration } of stages) {
      await new Promise(resolve => setTimeout(resolve, duration));
      updateProgress(progress, stage);
    }
    
    // Complete optimization with mock results
    setTimeout(() => {
      completeOptimization({
        optimizedPrompt: `[OPTIMIZED] ${promptText} - Enhanced with better context, clearer instructions, and improved structure for maximum LLM performance.`,
        successRate: 85 + Math.floor(Math.random() * 10),
        improvement: 1.5 + Math.random()
      });
      // Navigate back to dashboard
      onNavigate('dashboard');
    }, 500);
  };

  const stages = [
    { key: 'initializing', label: 'Initializing', icon: '⚙️' },
    { key: 'generating', label: 'Generating Variations', icon: '🔄' },
    { key: 'testing', label: 'Testing Performance', icon: '🧪' },
    { key: 'critiquing', label: 'Analyzing Results', icon: '🔍' },
    { key: 'refining', label: 'Refining Prompts', icon: '✨' },
    { key: 'evaluating', label: 'Final Evaluation', icon: '📊' },
    { key: 'complete', label: 'Complete', icon: '✅' }
  ];

  if (isOptimizing && currentOptimization) {
    // Show optimization progress
    return (
      <div className="space-y-8">
        <div className="flex items-center gap-4">
          <button
            onClick={() => onNavigate('dashboard')}
            className="text-gray-600 hover:text-gray-900 transition-colors"
          >
            ← Back
          </button>
          <h1 className="text-heading-xl text-gray-900">Optimization in Progress</h1>
        </div>

        <Card>
          <CardContent className="p-6">
            <div className="space-y-8">
              <div>
                <h3 className="text-heading-md text-gray-900 mb-3">{currentOptimization.title}</h3>
                <p className="text-body-md text-gray-600 bg-gray-50 p-4 rounded-lg">
                  {currentOptimization.originalPrompt}
                </p>
              </div>

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h4 className="text-heading-md text-gray-900">Optimization Progress</h4>
                  <span className="text-body-sm text-gray-500">
                    Est. 2-3 minutes remaining
                  </span>
                </div>
                <Progress value={currentOptimization.progress || 0} showLabel className="h-3" />
              </div>

              <div className="space-y-3">
                {stages.map((stageItem, index) => {
                  const isActive = currentOptimization.stage === stageItem.key;
                  const isPast = stages.findIndex(s => s.key === currentOptimization.stage) > index;

                  return (
                    <div
                      key={stageItem.key}
                      className={`flex items-center space-x-4 p-4 rounded-lg transition-colors ${
                        isActive ? 'bg-primary-50 border border-primary-200' :
                        isPast ? 'opacity-50' : 'hover:bg-gray-50'
                      }`}
                    >
                      <span className="text-xl">{stageItem.icon}</span>
                      <span className="text-body-md font-medium flex-1">{stageItem.label}</span>
                      {isActive && <Spinner className="ml-auto" size="sm" />}
                      {isPast && <span className="text-success-500">✓</span>}
                    </div>
                  );
                })}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center gap-4">
        <button
          onClick={() => onNavigate('dashboard')}
          className="text-gray-600 hover:text-gray-900 transition-colors"
        >
          ← Back to Dashboard
        </button>
        <h1 className="text-heading-xl text-gray-900">New Optimization</h1>
      </div>

      {/* Prompt Configuration */}
      <div>
        <div className="flex items-center gap-2 mb-6">
          <span className="text-2xl">📝</span>
          <h2 className="text-heading-lg text-gray-900">Prompt Configuration</h2>
        </div>
        <Card>
          <CardContent className="p-6">
            <div className="space-y-6">
              <div>
                <label className="block text-body-md font-medium text-gray-700 mb-2">
                  Optimization Title
                </label>
                <Input
                  placeholder="e.g., Marketing Email Generator"
                  value={promptTitle}
                  onChange={(e) => setPromptTitle(e.target.value)}
                  className="text-body-md"
                />
              </div>

              <div>
                <label className="block text-body-md font-medium text-gray-700 mb-2">
                  Original Prompt
                </label>
                <Textarea
                  placeholder="Write a marketing email for our new product..."
                  value={promptText}
                  onChange={(e) => setPromptText(e.target.value)}
                  className="min-h-[200px] text-body-md"
                />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Optimization Settings */}
      <div>
        <div className="flex items-center gap-2 mb-6">
          <span className="text-2xl">⚙️</span>
          <h2 className="text-heading-lg text-gray-900">Optimization Settings</h2>
        </div>
        <Card>
          <CardContent className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-body-md font-medium text-gray-700 mb-2">
                  Technique
                </label>
                <select
                  className="w-full px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 text-body-md"
                  value={config.technique}
                  onChange={(e) => setConfig({...config, technique: e.target.value})}
                >
                  <option value="critique_n_refine">Critique & Refine ▼</option>
                </select>
              </div>

              <div>
                <label className="block text-body-md font-medium text-gray-700 mb-2">
                  Iterations: <span className="font-normal">{config.iterations}</span>
                </label>
                <div className="flex items-center gap-2">
                  <span className="text-body-sm text-gray-500">●●●○○</span>
                  <input
                    type="range"
                    min="1"
                    max="5"
                    value={config.iterations}
                    onChange={(e) => setConfig({...config, iterations: parseInt(e.target.value)})}
                    className="flex-1 h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                  />
                  <span className="text-body-sm text-gray-500">{config.iterations}</span>
                </div>
              </div>

              <div>
                <label className="block text-body-md font-medium text-gray-700 mb-2">
                  Batch Size: <span className="font-normal">{config.batchSize} questions</span>
                </label>
                <div className="flex items-center gap-2">
                  <span className="text-body-sm text-gray-500">●●○○○</span>
                  <input
                    type="range"
                    min="5"
                    max="20"
                    step="5"
                    value={config.batchSize}
                    onChange={(e) => setConfig({...config, batchSize: parseInt(e.target.value)})}
                    className="flex-1 h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                  />
                  <span className="text-body-sm text-gray-500">{config.batchSize}</span>
                </div>
              </div>

              <div>
                <label className="block text-body-md font-medium text-gray-700 mb-2">
                  Success Threshold: <span className="font-normal">{config.successThreshold}%</span>
                </label>
                <div className="flex items-center gap-2">
                  <span className="text-body-sm text-gray-500">●●●●○</span>
                  <input
                    type="range"
                    min="70"
                    max="95"
                    step="5"
                    value={config.successThreshold}
                    onChange={(e) => setConfig({...config, successThreshold: parseInt(e.target.value)})}
                    className="flex-1 h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                  />
                  <span className="text-body-sm text-gray-500">{config.successThreshold}%</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Action Buttons */}
      <div className="flex justify-end gap-4">
        <Button variant="secondary" size="lg">
          Save Draft
        </Button>
        <Button
          onClick={handleStartOptimization}
          disabled={!promptText || !promptTitle || isOptimizing}
          size="lg"
        >
          Start Optimization
        </Button>
      </div>
    </div>
  );
}