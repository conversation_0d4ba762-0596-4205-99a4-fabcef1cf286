import React from 'react';
import { Card, CardContent } from '../ui/Card';
import { Button } from '../ui/Button';
import { Badge } from '../ui/Badge';
import { useOptimizationStore } from '../../store/optimizationStore';
import { formatDate } from '../../lib/utils';

export function Dashboard({ onNavigate }) {
  const { optimizationHistory, stats } = useOptimizationStore();
  
  return (
    <div className="space-y-8">
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-8">
          {/* Quick Stats Section */}
          <div>
            <div className="flex items-center gap-2 mb-6">
              <span className="text-2xl">📊</span>
              <h2 className="text-heading-lg text-gray-900">Quick Stats</h2>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <Card className="text-center">
                <CardContent className="py-6">
                  <div className="text-display-sm font-bold text-primary-600">{stats.totalOptimizations}</div>
                  <div className="text-body-md text-gray-600 mt-1">Optimizations</div>
                </CardContent>
              </Card>
              <Card className="text-center">
                <CardContent className="py-6">
                  <div className="text-display-sm font-bold text-success-600">{stats.avgSuccessRate}%</div>
                  <div className="text-body-md text-gray-600 mt-1">Avg Success</div>
                </CardContent>
              </Card>
              <Card className="text-center">
                <CardContent className="py-6">
                  <div className="text-display-sm font-bold text-accent-600">{stats.avgImprovement}x</div>
                  <div className="text-body-md text-gray-600 mt-1">Avg Improve</div>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Recent Optimizations Section */}
          <div>
            <div className="flex items-center gap-2 mb-6">
              <span className="text-2xl">📈</span>
              <h2 className="text-heading-lg text-gray-900">Recent Optimizations</h2>
            </div>
            <Card>
              <CardContent className="p-0">
                <div className="divide-y divide-gray-200">
                  {optimizationHistory.map((item) => (
                    <div key={item.id} className="p-6 hover:bg-gray-50 transition-colors">
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-3">
                            <h3 className="text-body-lg font-semibold text-gray-900">{item.title}</h3>
                            {item.status === 'complete' ? (
                              <Badge variant="success">✅ {item.successRate}% Success</Badge>
                            ) : item.status === 'processing' ? (
                              <Badge variant="warning">🔄 Processing...</Badge>
                            ) : (
                              <Badge>{item.status}</Badge>
                            )}
                          </div>
                        </div>
                        <div className="text-body-sm text-gray-500">
                          {formatDate(item.timestamp)}
                        </div>
                      </div>
                    </div>
                  ))}

                  {optimizationHistory.length === 0 && (
                    <div className="text-center py-12 text-gray-500">
                      <div className="text-body-lg">No optimizations yet.</div>
                      <div className="text-body-sm mt-1">Start your first optimization!</div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Sidebar */}
        <div className="space-y-8">
          {/* Quick Actions */}
          <div>
            <div className="flex items-center gap-2 mb-6">
              <span className="text-2xl">🚀</span>
              <h2 className="text-heading-lg text-gray-900">Quick Actions</h2>
            </div>
            <Card>
              <CardContent className="p-6">
                <div className="space-y-3">
                  <Button
                    onClick={() => onNavigate('optimizer')}
                    size="lg"
                    className="w-full justify-center"
                  >
                    New Optimization
                  </Button>
                  <Button variant="secondary" className="w-full justify-center">
                    View Templates
                  </Button>
                  <Button variant="secondary" className="w-full justify-center">
                    Import Prompts
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
