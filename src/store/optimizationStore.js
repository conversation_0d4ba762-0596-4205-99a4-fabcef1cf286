import { create } from 'zustand';

export const useOptimizationStore = create((set, get) => ({
  // Current optimization state
  currentOptimization: null,
  optimizationHistory: [
    // Mock data for demo
    {
      id: 1,
      title: 'Marketing Copy Optimization',
      status: 'complete',
      successRate: 94,
      improvement: 2.4,
      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
      originalPrompt: 'Write a marketing email for our new product...',
      optimizedPrompt: 'Create a compelling marketing email that highlights the unique value proposition of our new product...'
    },
    {
      id: 2,
      title: 'Code Review Prompts',
      status: 'complete',
      successRate: 87,
      improvement: 1.8,
      timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000), // 1 day ago
      originalPrompt: 'Review this code for bugs...',
      optimizedPrompt: 'Perform a comprehensive code review focusing on potential bugs, security vulnerabilities, and performance issues...'
    },
    {
      id: 3,
      title: 'Customer Support Scripts',
      status: 'processing',
      successRate: null,
      improvement: null,
      timestamp: new Date(),
      progress: 45,
      stage: 'critiquing',
      originalPrompt: 'Help customer with billing issue...',
      optimizedPrompt: null
    }
  ],
  isOptimizing: false,
  
  // Stats
  stats: {
    totalOptimizations: 24,
    avgSuccessRate: 89,
    avgImprovement: 2.3
  },
  
  // Actions
  startOptimization: (promptData) => set({ 
    isOptimizing: true,
    currentOptimization: { 
      ...promptData, 
      id: Date.now(),
      status: 'initializing',
      stage: 'initializing',
      progress: 0,
      timestamp: new Date()
    }
  }),
  
  updateProgress: (progress, stage) => set((state) => ({
    currentOptimization: state.currentOptimization ? { 
      ...state.currentOptimization, 
      progress,
      stage,
      status: progress === 100 ? 'complete' : 'processing'
    } : null
  })),
  
  completeOptimization: (results) => set((state) => {
    const completed = {
      ...state.currentOptimization,
      ...results,
      status: 'complete',
      progress: 100
    };
    
    return {
      isOptimizing: false,
      currentOptimization: null,
      optimizationHistory: [completed, ...state.optimizationHistory]
    };
  }),
  
  cancelOptimization: () => set({
    isOptimizing: false,
    currentOptimization: null
  })
}));