import React, { useState } from 'react';
import { Dashboard } from './components/features/Dashboard';
import { PromptOptimizer } from './components/features/PromptOptimizer';
import './App.css';

function App() {
  const [currentView, setCurrentView] = useState('dashboard');
  
  // Diagnostic logging
  console.log('App component mounted successfully');
  console.log('Current view:', currentView);

  return (
    <div className="min-h-screen bg-gray-50 font-system">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center gap-3">
              <span className="text-2xl">🧙</span>
              <h1 className="text-heading-md font-bold text-gray-900">PromptWizard</h1>
            </div>
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center text-white text-body-sm font-medium">
                U
              </div>
              <span className="text-body-sm font-medium text-gray-700">[Profile]</span>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {currentView === 'dashboard' && (
          <Dashboard onNavigate={setCurrentView} />
        )}
        {currentView === 'optimizer' && (
          <PromptOptimizer onNavigate={setCurrentView} />
        )}
      </main>
    </div>
  );
}

export default App;
