# Use a Node.js image as the base for the frontend
FROM node:22-alpine

# Set the working directory in the container
WORKDIR /app

# Copy the frontend application code
COPY src/ ./

# Copy package.json and package-lock.json from the root of the project, overwriting the one from src
COPY package.json ./

# Install frontend dependencies
RUN npm install
RUN npm install @tailwindcss/postcss

# List the files in the /app directory
RUN ls -la

# Expose the port Vite serves on
EXPOSE 5173

# Command to run the application in development mode
CMD ["npm", "run", "dev"]
