# PromptWizard

## Project Overview

PromptWizard is an AI-powered prompt optimization platform designed to help users craft, test, and refine prompts for large language models. This project is being restructured to feature a modern, user-friendly interface built with React, Vite, and Tailwind CSS, backed by a robust FastAPI backend.

## Architecture

The project adheres to a Clean Architecture approach for the backend (FastAPI) and a simplified Atomic Design approach for the frontend (React).

### Backend (FastAPI)

- **Framework:** FastAPI
- **Language:** Python 3.12+
- **Database:** PostgreSQL
- **ORM:** SQLAlchemy with Alembic for migrations
- **Caching:** Redis
- **Background Tasks:** Celery

### Frontend (React)

- **Framework:** React with Vite
- **Language:** JavaScript (ESNext)
- **CSS Framework:** TailwindCSS
- **UI Components:** Shadcn (planned)

## Getting Started

### Prerequisites

- Docker Desktop (for containerized development)
- Node.js (v22.x+ LTS) and npm (v11.x+)
- Python 3.12+ and pip

### Local Development with Docker Compose

1.  **Clone the repository:**
    ```bash
    git clone https://github.com/Leonai-do/PromptWizard.git
    cd PromptWizard
    ```

2.  **Create a `.env` file:**
    Copy the `.env.example` to `.env` and fill in your environment variables.
    ```bash
    cp .env.example .env
    ```

3.  **Build and run the Docker containers:**
    ```bash
    docker-compose up --build
    ```
    This will start the PostgreSQL database, Redis, FastAPI backend, and React frontend.

4.  **Access the applications:**
    -   **FastAPI Backend:** `http://localhost:8000/api/v1/docs` (for API documentation)
    -   **React Frontend:** `http://localhost:5173`

### Local Development (without Docker)

#### Backend Setup

1.  **Create and activate a Python virtual environment:**
    ```bash
    python3 -m venv venv
    source venv/bin/activate
    ```

2.  **Install Python dependencies:**
    ```bash
    pip install -r requirements.txt
    ```

3.  **Run database migrations (if any):**
    ```bash
    # Placeholder for Alembic commands
    # alembic upgrade head
    ```

4.  **Start the FastAPI backend:**
    ```bash
    uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
    ```

#### Frontend Setup

1.  **Navigate to the frontend directory:**
    ```bash
    cd src
    ```

2.  **Install Node.js dependencies:**
    ```bash
    npm install
    ```

3.  **Start the React development server:**
    ```bash
    npm run dev
    ```

## Project Structure

```
/PromptWizard
├── .github/                # GitHub-specific files, including CI/CD workflows and templates.
│   ├── workflows/          # GitHub Actions for CI/CD.
│   │   └── main.yml
│   └── pull_request_template.md # The mandatory template for all PRs.
│
├── app/                    # The main application source code (FastAPI backend).
│   ├── api/                # API layer: routers, endpoints, dependencies.
│   ├── core/               # Core business logic (no framework dependencies).
│   ├── db/                 # Database layer: SQLAlchemy models and Alembic migrations.
│   └── schemas/            # Pydantic schemas for data validation.
│
├── src/                    # The main application source code (React frontend).
│   └── components/
│       ├── ui/             # Atoms (e.g., Button.jsx)
│       ├── composed/       # Molecules (e.g., SearchBar.jsx)
│       └── features/       # Organisms (e.g., UserProfileCard.jsx)
│
├── tests/                  # All backend tests.
│   ├── core/
│   └── api/
│
├── .dockerignore           # Specifies files to exclude from Docker builds.
├── .env.example            # Template for required environment variables.
├── .gitignore              # Specifies files to be ignored by Git.
├── docker-compose.yml      # Defines and runs multi-container Docker applications.
├── Dockerfile              # Instructions to build the application's Docker image.
├── Dockerfile.frontend     # Instructions to build the frontend's Docker image.
├── package.json            # Frontend project manifest and dependencies.
└── README.md               # Project overview and setup instructions.
```

## Contributing

Please refer to `CONTRIBUTING.md` (if exists) and the `.github/pull_request_template.md` for contribution guidelines.

## License

This project is licensed under the [LICENSE_TYPE] License - see the `LICENSE` file for details.
