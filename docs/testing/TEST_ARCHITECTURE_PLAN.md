# PromptWizard Test Architecture Plan

Objective  
Design a comprehensive, automated testing strategy that verifies every aspect of the application from first deployment to advanced prompt optimization. This plan covers test layers, tooling, fixtures, CI integration, environments, coverage targets, reporting, and continuous quality gates.

## Repository References (live paths)

- Backend entrypoint: [app/main.py](app/main.py)
- Backend core: [app/core/](app/core/)
- API layer: [app/api/](app/api/)
- Schemas: [app/schemas/](app/schemas/)
- DB layer: [app/db/](app/db/)
- Frontend root: [src/](src/)
- CI pipeline: [.github/workflows/main.yml](.github/workflows/main.yml)
- Docker compose: [docker-compose.yml](docker-compose.yml)
- Python deps: [requirements.txt](requirements.txt)
- Test roots: [tests/core/](tests/core/), [tests/api/](tests/api/)

## Governance & Targets (from .dev-rules/AGENTS.md)

- Coverage: core logic ≥ 90%, overall ≥ 80%
- Gates in CI: Lint → Test → Build → Deploy (fail-fast at each stage)
- API-first: Contracts validated (OpenAPI), versioned (/api/v1)
- Logging: Structured JSON with request_id, timings
- Security: CORS strict, rate limiting enabled, secrets never logged

---

## 1) Test Pyramid and Layers

1. Pre-commit gates (local developer loop)
   - Python: ruff, black, isort, mypy
   - JS/React: eslint + prettier (+ tailwind plugin), type checks if TS added
2. Unit tests (fast, isolated)
   - Python: pytest for utility modules, LLM helpers, prompt optimization, param logger, schemas
   - JS: vitest + React Testing Library for components, hooks, utilities
3. Integration tests (intra-service boundaries)
   - Backend: FastAPI routers with httpx AsyncClient, in-memory or ephemeral Postgres
   - CORS, rate limiting (slowapi), global exception handlers, JSON log structure
4. Contract tests
   - Validate OpenAPI (/api/v1/openapi.json); ensure docs mount toggles by ENV
5. E2E tests
   - Playwright or Cypress across React (Vite) ↔ FastAPI through docker-compose
6. Performance tests
   - k6 or Locust against critical endpoints and user flows
7. Security/static analysis
   - Python: bandit, safety
   - Node: npm audit (warn for moderate, fail for critical by policy)

---

## 2) Environments & Data Management

- Local (dev):
  - Unit + integration default; DB can be sqlite/ephemeral PG
  - MSW for frontend API mocking
- CI:
  - Same as dev with isolated containers from [docker-compose.yml](docker-compose.yml)
  - Artifacts uploaded (coverage, junit)
- Test configuration:
  - `.env.test` derived from [.env.example](../../.env.example)
  - Deterministic settings: disable caches, fixed log level, seeded randomness
- Test data:
  - Factory Boy + Faker for backend domain entities
  - Golden datasets (prompts & expected outcomes) under `tests/assets/`

---

## 3) Backend (Python / Pytest)

### 3.1 Core Utilities — [app/core/glue/common/utils/](../../app/core/glue/common/utils/)
- Modules: `file.py`, `logging.py`, `runtime_tasks.py`, `download.py`
- Verify I/O via tmp paths; test negative/error paths; ensure logs output JSON structure

### 3.2 Constants & Exceptions — [app/core/glue/common/constants/](../../app/core/glue/common/constants/), [exceptions.py](../../app/core/glue/common/exceptions.py)
- Ensure constants exist and have values used by other layers
- Exception hierarchy, messages, and mapping to HTTP codes (if applicable)

### 3.3 LLM Helpers/Manager — [app/core/glue/common/llm/](../../app/core/glue/common/llm/)
- `llm_helper.py`, `llm_mgr.py`, `promptMessage.py`
- Mock LLM invocations; seed random; assert retries/backoff (if implemented); verify prompt message formatting

### 3.4 Param Logger — [app/core/glue/paramlogger/](../../app/core/glue/paramlogger/)
- Validate safe writes, rotation rules if any; malformed params rejected; atomic file ops

### 3.5 Prompt Optimization — [app/core/glue/promptopt/](../../app/core/glue/promptopt/)
- Techniques:
  - `critique_n_refine`: base/core logic; param bounds; error conditions
  - `instantiate.py`, `runner.py`: orchestration (hooks/sequences)
- Golden tests:
  - Given baseline prompt + seed, optimized prompt meets minimum mock “quality” score (deterministic evaluator)

### 3.6 Schemas — [app/schemas/](../../app/schemas/)
- Pydantic validation rules: types, lengths, enums, formats; negative/edge cases

### 3.7 API Routers — [app/api/](../../app/api/)
- Integration with httpx AsyncClient and FastAPI lifespan:
  - Health endpoints: `/health`, `/health/detailed` structure
  - 404/500 exception shapes (type/message/path)
  - CORS headers for allowed origins
  - Rate limiting (429), proper headers/cooldowns
  - Trusted Host enforcement

### 3.8 App Wiring — [app/main.py](../../app/main.py)
- OpenAPI/docs toggled by ENV
- JSON logging fields: request_id, timings, level, path
- Lifespan creates necessary directories (UPLOAD_DIR, TEMP_DIR)

### 3.9 Database Layer — [app/db/](../../app/db/)
- Migration sanity (alembic, if configured)
- Basic CRUD with rollback fixture; isolation between tests

### 3.10 Background Tasks (Celery/Redis)
- Unit: tasks logic with fakes
- Integration: containerized broker if needed, or stub layer

#### Backend Mocking Strategy
- LLMs: monkeypatch helpers to deterministic results
- External: fakeredis or stub clients; PG via fixtures or containers
- Time: freezegun/time-machine for reproducible timestamps
- Random: global seed fixture

---

## 4) Frontend (Vite/React / Vitest + RTL)

### 4.1 Unit Tests (Components) — [src/components/](../../src/components/)
- Atoms: render/props/ARIA; Tailwind critical classes presence
- Molecules & Features: compose behaviors; interactions events; snapshot only for stable non-CSS-critical pieces

### 4.2 Hooks/Utils — [src/](../../src/)
- Data fetching hooks: happy path, loading, error, cancellation
- Pure utils: formatting/validation

### 4.3 Frontend Integration (MSW)
- Full flows with mocked backend:
  - Submit prompt → receive optimized response → render metrics
  - Error surfaces and retry
  - Routing/guarded views (when implemented)

### 4.4 E2E (Playwright)
- Bring up full stack with [docker-compose.yml](../../docker-compose.yml)
- Smoke:
  - Home page renders
  - Create prompt, submit, see optimized result
  - Hard refresh preserves expected state (if persisted)
- Record traces/video on failure

---

## 5) Prompt Optimization Acceptance & Quality

- Golden Suites:
  - Curated prompts and expected improvements (e.g., token count, section completeness, mock quality score)
- Deterministic Evaluator:
  - Mock scoring to avoid live LLM dependency in CI
- A/B Harness:
  - Baseline vs optimized metrics; assert Δ improvement
- Regression Guards:
  - Snapshot optimized prompt metadata (token length, headers present)
- Parameter Space:
  - Parametrized tests across technique params and boundary extremes

---

## 6) Performance & Reliability

- k6/Locust scripts:
  - Targets: `/api/v1/...` primary prompt endpoints; p95/p99 thresholds
- Resiliency Tests:
  - Network timeouts; retry policies in LLM helper; no unhandled exceptions
- Rate Limiting:
  - Sustain traffic → 429; assert headers and cool-down behavior
- Resource Leaks:
  - Long-run integration verifying no file-descriptor or handle leaks (periodic runs or nightly)

---

## 7) Security & Compliance

- Static Analysis:
  - Python: bandit (fail high), safety
  - Node: npm audit (warn moderate, fail critical)
- CORS:
  - Disallow unlisted origins; preflight handling
- Trusted Hosts:
  - Only allow configured hosts
- Logging:
  - Ensure no secrets or tokens appear in logs; unit tests on logging formatter

---

## 8) Fixtures, Factories & Test Utilities

- Pytest Fixtures:
  - `app_client` (AsyncClient + lifespan)
  - `db_session` (transaction per test + rollback)
  - `redis_fake` (fakeredis) or compose service for integration
  - `llm_fake` (deterministic)
  - `seed` fixture for randomness
- Factories:
  - factory-boy + faker for DB entities
- Assets:
  - `tests/assets/prompt_sets/` for golden prompts & expected results

---

## 9) Reporting, Artifacts & Quality Gates (CI)

- Coverage:
  - pytest-cov: XML + HTML
  - vitest: lcov + HTML
  - Thresholds: core ≥ 90%; overall ≥ 80% (CI enforce)
- Results:
  - JUnit XML exports for both Python/JS
- Artifacts:
  - Upload coverage & HTML reports in CI job
- CI Stages (in [.github/workflows/main.yml](../../.github/workflows/main.yml)):
  - Lint (Python + JS)
  - Unit (backend + frontend)
  - Integration (backend)
  - Build (vite build + Docker build)
  - E2E (on PR labels or nightly)
  - Perf/security (nightly or release branches)

---

## 10) Developer Workflow (TDD)

- Write tests first for new endpoints/features/optimization techniques
- Run unit tests on save: `pytest -k ...`, `vitest --watch`
- Use pre-commit hooks: lint/format/units
- Keep tests deterministic (seeded); avoid live network calls in CI

---

## 11) Deliverables to Implement Next

- TESTING.md (quick start, commands, conventions)
- Pytest scaffolds under [tests/core/](../../tests/core/) and [tests/api/](../../tests/api/)
  - `conftest.py` with shared fixtures
- Frontend tests in [src/](../../src/) (`__tests__` or co-located)
- MSW setup for frontend integration tests
- Playwright config + smoke suite
- Golden datasets under `tests/assets/prompt_sets/`
- CI enhancements to publish coverage reports and enforce thresholds

---

## 12) Execution Order (Incremental Roadmap)

1. Lint/type gates; baseline unit tests for backend utils/schemas and frontend atoms
2. Backend API integration (health/error/rate limiting/contracts)
3. Frontend integration with MSW for core flows
4. Prompt optimization unit + golden quality tests (deterministic evaluator)
5. E2E smoke via compose + Playwright
6. Performance & security nightly runs
7. Expand coverage to hit and hold targets across core modules

---

## 13) Command Reference (Suggested)

### Backend
- Unit/Integration:
  ```bash
  pytest -q --maxfail=1 --disable-warnings
  pytest -q tests/api/ --maxfail=1
  ```
- Coverage:
  ```bash
  pytest --cov=app --cov-report=xml --cov-report=html --cov-fail-under=80
  ```

### Frontend
- Unit:
  ```bash
  cd src
  npm run test
  ```
- Coverage:
  ```bash
  cd src
  npm run test -- --coverage
  ```

### E2E (example)
```bash
docker-compose up -d --build
# run playwright tests
# docker-compose down -v
```

---

This plan is aligned with the blueprint in [.dev-rules/AGENTS.md](../../.dev-rules/AGENTS.md), ensuring API-first contracts, coverage targets, security policies, and CI quality gates. It provides actionable steps to fully test deployment, backend/DB/caching, frontend UI, API contracts, performance, security, and prompt optimization quality end-to-end.