# PromptWizard UI/UX Design Blueprint

> Version: 1.0.0  
> Last Updated: 2025-01-27  
> Scope: Comprehensive design system and user experience guidelines for PromptWizard AI prompt optimization platform

## 1. Project Overview & Context

### Core Functionality
PromptWizard is an AI-powered prompt optimization platform that uses advanced critique-and-refine techniques to automatically improve prompts for large language models. The platform employs a self-evolving mechanism where the LLM generates, critiques, and refines its own prompts through iterative feedback loops.

**Key Technical Features:**
- **Critique & Refine Algorithm**: Automated prompt optimization using the `CRITIQUE_N_REFINE` technique
- **Batch Processing**: Configurable question batch sizes and evaluation rounds
- **Performance Analytics**: Detailed metrics on prompt improvement and success rates
- **Multi-Model Support**: Integration with various LLM providers

### Target User Personas

#### Primary Users
1. **AI Engineers** - Building production AI systems requiring optimized prompts
2. **Prompt Engineers** - Specialists focused on prompt crafting and optimization
3. **Data Scientists** - Researchers needing reliable prompt performance for experiments

#### Secondary Users
4. **Business Analysts** - Non-technical users requiring AI-powered insights
5. **Product Managers** - Overseeing AI feature development and performance

### Value Propositions
- **Automated Optimization**: Eliminates manual prompt trial-and-error
- **Scientific Approach**: Data-driven optimization with measurable improvements
- **Time Efficiency**: Reduces prompt development time by 70-80%
- **Performance Transparency**: Clear metrics on optimization success rates

## 2. Technical Architecture Alignment

### Frontend Component Structure (Atomic Design)

```javascript
// src/components/ui/index.js - Atoms - Base reusable components
export { Button } from './Button';
export { Input } from './Input';
export { Badge } from './Badge';
export { Progress } from './Progress';
export { Spinner } from './Spinner';
export { Card } from './Card';
export { Alert } from './Alert';
```

```javascript
// src/components/composed/index.js - Molecules - Combinations of atoms
export { SearchBar } from './SearchBar';
export { PromptCard } from './PromptCard';
export { OptimizationProgress } from './OptimizationProgress';
export { MetricsDisplay } from './MetricsDisplay';
export { NavigationMenu } from './NavigationMenu';
```

```javascript
// src/components/features/index.js - Organisms - Feature-specific components
export { PromptOptimizer } from './PromptOptimizer';
export { ResultsAnalyzer } from './ResultsAnalyzer';
export { DashboardOverview } from './DashboardOverview';
export { OptimizationHistory } from './OptimizationHistory';
```

### Backend Integration Points

**Key API Endpoints:**
- `POST /api/v1/optimize` - Submit prompt for optimization
- `GET /api/v1/optimization/{id}/status` - Real-time progress tracking
- `GET /api/v1/optimization/{id}/results` - Retrieve optimization results
- `GET /api/v1/history` - User optimization history

### State Management Strategy

```javascript
// src/store/optimizationStore.js
import { create } from 'zustand';

export const useOptimizationStore = create((set, get) => ({
  // Current optimization state
  currentOptimization: null,
  optimizationHistory: [],
  isOptimizing: false,
  
  // Actions
  startOptimization: (promptData) => set({ 
    isOptimizing: true,
    currentOptimization: { ...promptData, status: 'initializing' }
  }),
  
  updateProgress: (progress) => set((state) => ({
    currentOptimization: { 
      ...state.currentOptimization, 
      progress 
    }
  })),
  
  completeOptimization: (results) => set((state) => ({
    isOptimizing: false,
    optimizationHistory: [results, ...state.optimizationHistory]
  }))
}));
```

### Responsive Design Requirements
- **Desktop First**: Primary experience optimized for 1440px+ screens
- **Tablet Support**: Responsive down to 768px with adapted layouts
- **Mobile Compatibility**: Essential features accessible on 375px+ devices
- **Progressive Enhancement**: Core functionality works without JavaScript

## 3. Design System Specifications

### Color Palette

**Primary Colors (AI/ML Aesthetic):**
```css
:root {
  /* Primary Brand */
  --color-primary-50: #f0f9ff;
  --color-primary-500: #3b82f6;
  --color-primary-600: #2563eb;
  --color-primary-900: #1e3a8a;
  
  /* Accent (AI/Neural Network) */
  --color-accent-400: #a855f7;
  --color-accent-500: #9333ea;
  --color-accent-600: #7c3aed;
  
  /* Success/Optimization */
  --color-success-400: #4ade80;
  --color-success-500: #22c55e;
  --color-success-600: #16a34a;
  
  /* Warning/Processing */
  --color-warning-400: #facc15;
  --color-warning-500: #eab308;
  
  /* Error/Failed */
  --color-error-400: #f87171;
  --color-error-500: #ef4444;
  
  /* Neutral/Interface */
  --color-gray-50: #f9fafb;
  --color-gray-100: #f3f4f6;
  --color-gray-200: #e5e7eb;
  --color-gray-500: #6b7280;
  --color-gray-700: #374151;
  --color-gray-900: #111827;
}
```

### Typography Hierarchy

```css
/* src/styles/typography.css */
/* System Font Stack */
.font-system {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 
               'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
}

/* Typography Scale */
.text-display-lg { font-size: 3.75rem; line-height: 1; font-weight: 800; }
.text-display-md { font-size: 3rem; line-height: 1.1; font-weight: 700; }
.text-heading-xl { font-size: 2.25rem; line-height: 1.2; font-weight: 600; }
.text-heading-lg { font-size: 1.875rem; line-height: 1.3; font-weight: 600; }
.text-heading-md { font-size: 1.5rem; line-height: 1.4; font-weight: 500; }
.text-body-lg { font-size: 1.125rem; line-height: 1.6; font-weight: 400; }
.text-body-md { font-size: 1rem; line-height: 1.6; font-weight: 400; }
.text-body-sm { font-size: 0.875rem; line-height: 1.5; font-weight: 400; }
.text-caption { font-size: 0.75rem; line-height: 1.4; font-weight: 500; }
```

### Component Library Structure (Shadcn Integration)

```jsx
// src/components/ui/Button.jsx
import { cn } from '@/lib/utils';

const buttonVariants = {
  default: 'bg-primary-600 text-white hover:bg-primary-700',
  secondary: 'bg-gray-100 text-gray-900 hover:bg-gray-200',
  accent: 'bg-accent-500 text-white hover:bg-accent-600',
  ghost: 'hover:bg-gray-100 text-gray-700'
};

export function Button({ 
  variant = 'default', 
  size = 'md', 
  className, 
  children, 
  ...props 
}) {
  return (
    <button
      className={cn(
        'inline-flex items-center justify-center rounded-md font-medium transition-colors',
        'focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary-500',
        'disabled:pointer-events-none disabled:opacity-50',
        buttonVariants[variant],
        size === 'sm' && 'h-8 px-3 text-sm',
        size === 'md' && 'h-10 px-4 text-sm',
        size === 'lg' && 'h-12 px-6 text-base',
        className
      )}
      {...props}
    >
      {children}
    </button>
  );
}
```

### Accessibility Standards (WCAG 2.1 AA)

**Key Requirements:**
- **Color Contrast**: Minimum 4.5:1 ratio for normal text, 3:1 for large text
- **Keyboard Navigation**: All interactive elements accessible via keyboard
- **Screen Reader Support**: Proper ARIA labels and semantic HTML
- **Focus Management**: Visible focus indicators and logical tab order
- **Alternative Text**: Descriptive alt text for all images and icons

## 4. User Experience Flow

### Complete User Journey

```mermaid
graph TD
    A[Landing Page] --> B[Sign Up/Login]
    B --> C[Onboarding Tutorial]
    C --> D[Dashboard]
    D --> E[Create New Optimization]
    E --> F[Prompt Input & Configuration]
    F --> G[Start Optimization]
    G --> H[Real-time Progress Tracking]
    H --> I[Results Analysis]
    I --> J[Save/Export Results]
    J --> D
    
    D --> K[View History]
    K --> L[Compare Results]
    L --> D
```

### Key Screen Wireframes

#### Dashboard Overview
```
┌─────────────────────────────────────────────────────────────┐
│ PromptWizard                                    [Profile] │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  📊 Quick Stats                    🚀 Quick Actions        │
│  ┌─────────────────┐              ┌─────────────────┐      │
│  │ 24 Optimizations│              │ [New Optimization]│     │
│  │ 89% Avg Success │              │ [View Templates]  │     │
│  │ 2.3x Avg Improve│              │ [Import Prompts]  │     │
│  └─────────────────┘              └─────────────────┘      │
│                                                             │
│  📈 Recent Optimizations                                   │
│  ┌─────────────────────────────────────────────────────────┐│
│  │ Marketing Copy Optimization    ✅ 94% Success   2h ago ││
│  │ Code Review Prompts           ✅ 87% Success   1d ago ││
│  │ Customer Support Scripts      🔄 Processing...        ││
│  └─────────────────────────────────────────────────────────┘│
└─────────────────────────────────────────────────────────────┘
```

#### Prompt Input Interface
```
┌─────────────────────────────────────────────────────────────┐
│ ← Back to Dashboard              New Optimization          │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  📝 Prompt Configuration                                   │
│  ┌─────────────────────────────────────────────────────────┐│
│  │ Original Prompt                                         ││
│  │ ┌─────────────────────────────────────────────────────┐ ││
│  │ │ Write a marketing email for our new product...     │ ││
│  │ │                                                     │ ││
│  │ └─────────────────────────────────────────────────────┘ ││
│  └─────────────────────────────────────────────────────────┘│
│                                                             │
│  ⚙️ Optimization Settings                                  │
│  ┌─────────────────────────────────────────────────────────┐│
│  │ Technique: [Critique & Refine ▼]                       ││
│  │ Iterations: [●●●○○] 3                                  ││
│  │ Batch Size: [●●○○○] 5 questions                       ││
│  │ Success Threshold: [●●●●○] 80%                         ││
│  └─────────────────────────────────────────────────────────┘│
│                                                             │
│                              [Start Optimization] [Save Draft]│
└─────────────────────────────────────────────────────────────┘
```

### Interactive States & Loading Patterns

**Optimization Progress States:**
1. **Initializing** - Setting up optimization parameters
2. **Generating Variations** - Creating prompt candidates
3. **Testing Performance** - Evaluating against test questions
4. **Critiquing Results** - Analyzing performance gaps
5. **Refining Prompts** - Improving based on critique
6. **Final Evaluation** - Testing optimized prompts
7. **Complete** - Results ready for review

```jsx
// src/components/composed/OptimizationProgress.jsx
export function OptimizationProgress({ stage, progress, estimatedTime }) {
  const stages = [
    { key: 'initializing', label: 'Initializing', icon: '⚙️' },
    { key: 'generating', label: 'Generating Variations', icon: '🔄' },
    { key: 'testing', label: 'Testing Performance', icon: '🧪' },
    { key: 'critiquing', label: 'Analyzing Results', icon: '🔍' },
    { key: 'refining', label: 'Refining Prompts', icon: '✨' },
    { key: 'evaluating', label: 'Final Evaluation', icon: '📊' },
    { key: 'complete', label: 'Complete', icon: '✅' }
  ];

  return (
    <div className='space-y-6'>
      <div className='flex items-center justify-between'>
        <h3 className='text-heading-md'>Optimization in Progress</h3>
        <span className='text-body-sm text-gray-500'>
          Est. {estimatedTime} remaining
        </span>
      </div>
      
      <Progress value={progress} className='h-2' />
      
      <div className='space-y-3'>
        {stages.map((stageItem, index) => (
          <div
            key={stageItem.key}
            className={cn(
              'flex items-center space-x-3 p-3 rounded-lg',
              stage === stageItem.key && 'bg-primary-50 border border-primary-200',
              stages.findIndex(s => s.key === stage) > index && 'opacity-50'
            )}
          >
            <span className='text-lg'>{stageItem.icon}</span>
            <span className='text-body-md'>{stageItem.label}</span>
            {stage === stageItem.key && <Spinner className='ml-auto' />}
          </div>
        ))}
      </div>
    </div>
  );
}
```

## 5. Core Application Sections

### Authentication & Onboarding

**Onboarding Flow:**
1. **Welcome Screen** - Value proposition and feature overview
2. **Account Setup** - Basic profile information
3. **First Prompt Tutorial** - Guided optimization walkthrough
4. **Dashboard Introduction** - Feature tour with tooltips

### Main Dashboard

**Key Components:**
- **Performance Metrics Widget** - Success rates, improvement averages
- **Recent Activity Feed** - Latest optimizations with status
- **Quick Actions Panel** - New optimization, templates, imports
- **Usage Analytics** - Monthly optimization counts and trends

### Prompt Input & Configuration

**Configuration Options:**
- **Optimization Technique** - Currently supports `CRITIQUE_N_REFINE`
- **Iteration Count** - Number of refinement rounds (1-5)
- **Batch Size** - Questions per evaluation batch (5-20)
- **Success Threshold** - Minimum performance improvement (70-95%)
- **Model Selection** - Target LLM for optimization

### Real-time Progress Tracking

**Progress Indicators:**
- **Overall Progress Bar** - Percentage completion
- **Stage Breakdown** - Current optimization phase
- **Live Metrics** - Success rates as they're calculated
- **Estimated Time** - Dynamic completion estimates

### Results Analysis

**Analysis Views:**
- **Before/After Comparison** - Original vs optimized prompts
- **Performance Metrics** - Success rates, improvement percentages
- **Iteration History** - Step-by-step optimization process
- **Export Options** - JSON, CSV, or formatted reports

## 6. Implementation Guidelines

### Component Naming Conventions

```jsx
// src/components/features/PromptOptimizer/PromptOptimizer.jsx
/**
 * Main prompt optimization interface component.
 * Handles prompt input, configuration, and optimization initiation.
 */
export function PromptOptimizer() {
  const [promptText, setPromptText] = useState('');
  const [config, setConfig] = useState({
    technique: 'critique_n_refine',
    iterations: 3,
    batchSize: 5,
    successThreshold: 80
  });
  
  const { startOptimization, isOptimizing } = useOptimizationStore();
  
  const handleStartOptimization = async () => {
    try {
      await startOptimization({
        prompt: promptText,
        config
      });
    } catch (error) {
      // Error handling
    }
  };

  return (
    <div className='space-y-6'>
      <PromptInput 
        value={promptText}
        onChange={setPromptText}
        placeholder='Enter your prompt to optimize...'
      />
      
      <OptimizationConfig
        config={config}
        onChange={setConfig}
      />
      
      <div className='flex justify-end space-x-3'>
        <Button variant='secondary'>Save Draft</Button>
        <Button 
          onClick={handleStartOptimization}
          disabled={!promptText || isOptimizing}
        >
          Start Optimization
        </Button>
      </div>
    </div>
  );
}
```

### File Organization

```
src/
├── components/
│   ├── ui/                    # Atoms (Button, Input, Card, etc.)
│   ├── composed/              # Molecules (SearchBar, PromptCard, etc.)
│   └── features/              # Organisms (PromptOptimizer, ResultsAnalyzer)
├── hooks/                     # Custom React hooks
├── store/                     # Zustand stores
├── lib/                       # Utilities and API clients
├── styles/                    # Global CSS and Tailwind config
└── pages/                     # Route components
```

### Performance Considerations

**Real-time Updates:**
- **WebSocket Connection** - Live optimization progress
- **Optimistic Updates** - Immediate UI feedback
- **Debounced Inputs** - Prevent excessive API calls
- **Virtual Scrolling** - Handle large optimization histories

### Testing Strategy

```jsx
// src/components/ui/Button.test.jsx
import { render, screen } from '@testing-library/react';
import { Button } from './Button';

describe('Button Component', () => {
  it('renders with correct variant styles', () => {
    render(<Button variant='primary'>Click me</Button>);
    
    const button = screen.getByRole('button');
    expect(button).toHaveClass('bg-primary-600');
  });

  it('handles click events', () => {
    const handleClick = vi.fn();
    render(<Button onClick={handleClick}>Click me</Button>);
    
    screen.getByRole('button').click();
    expect(handleClick).toHaveBeenCalledOnce();
  });

  it('is accessible via keyboard', () => {
    render(<Button>Click me</Button>);
    
    const button = screen.getByRole('button');
    button.focus();
    expect(button).toHaveFocus();
  });
});
```

---

This comprehensive UI/UX Design Blueprint provides the foundation for building a modern, user-friendly interface for PromptWizard that aligns with the established technical architecture and development standards. The design system emphasizes clarity, performance, and accessibility while supporting the complex workflow of AI prompt optimization.