<!DOCTYPE html>
<html lang="en"><head>
<meta charset="utf-8"/>
<meta content="width=device-width, initial-scale=1.0" name="viewport"/>
<link crossorigin="" href="https://fonts.gstatic.com/" rel="preconnect"/>
<link as="style" href="https://fonts.googleapis.com/css2?display=swap&amp;family=Inter%3Awght%40400%3B500%3B600%3B700&amp;family=Noto+Sans%3Awght%40400%3B500%3B700%3B900" onload="this.rel='stylesheet'" rel="stylesheet"/>
<title>PromptWizard Dashboard</title>
<link href="data:image/x-icon;base64," rel="icon" type="image/x-icon"/>
<script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
<style type="text/tailwindcss">
    :root {
      --pg-bg: #111418;
      --pg-surface: #1A1E23;
      --pg-border: #2C343D;
      --pg-text-primary: #FFFFFF;
      --pg-text-secondary: #9CA3AF;
      --pg-primary: #0D80F2;
      --pg-primary-hover: #0B6AD9;
      --pg-secondary: #283039;
    }
    body {
      font-family: 'Inter', "Noto Sans", sans-serif;
    }
  </style>
</head>
<body class="bg-[var(--pg-bg)] text-[var(--pg-text-primary)]">
<div class="flex min-h-screen">
<aside class="w-64 flex-shrink-0 bg-[var(--pg-surface)] p-6 flex flex-col justify-between">
<div>
<div class="flex items-center gap-2 mb-8">
<svg class="text-[var(--pg-primary)]" fill="none" height="28" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" viewBox="0 0 24 24" width="28" xmlns="http://www.w3.org/2000/svg"><path d="m12 3-c.94.94-1.5 2.2-1.5 3.5v0c0 1.3 1.12 2.5 2.5 2.5h0c1.3 0 2.5-1.12 2.5-2.5v0c0-1.3-.56-2.56-1.5-3.5Z"></path><path d="m19.5 10.5-c.94.94-1.5 2.2-1.5 3.5v0c0 1.3 1.12 2.5 2.5 2.5h0c1.3 0 2.5-1.12 2.5-2.5v0c0-1.3-.56-2.56-1.5-3.5Z"></path><path d="m4.5 10.5-c.94.94-1.5 2.2-1.5 3.5v0c0 1.3 1.12 2.5 2.5 2.5h0c1.3 0 2.5-1.12 2.5-2.5v0c0-1.3-.56-2.56-1.5-3.5Z"></path></svg>
<h1 class="text-xl font-bold">PromptWizard</h1>
</div>
<nav class="flex flex-col gap-2">
<a class="flex items-center gap-3 px-4 py-2.5 rounded-md bg-[var(--pg-primary)] text-white font-semibold" href="#">
<svg fill="none" height="24" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" viewBox="0 0 24 24" width="24" xmlns="http://www.w3.org/2000/svg"><path d="m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path><polyline points="9 22 9 12 15 12 15 22"></polyline></svg>
<span>Home</span>
</a>
<a class="flex items-center gap-3 px-4 py-2.5 rounded-md text-[var(--pg-text-secondary)] hover:bg-[var(--pg-secondary)] hover:text-white transition-colors" href="#">
<svg fill="none" height="24" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" viewBox="0 0 24 24" width="24" xmlns="http://www.w3.org/2000/svg"><path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"></path><polyline points="14 2 14 8 20 8"></polyline></svg>
<span>Prompts</span>
</a>
<a class="flex items-center gap-3 px-4 py-2.5 rounded-md text-[var(--pg-text-secondary)] hover:bg-[var(--pg-secondary)] hover:text-white transition-colors" href="#">
<svg fill="none" height="24" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" viewBox="0 0 24 24" width="24" xmlns="http://www.w3.org/2000/svg"><path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"></path><path d="M10 12h.01"></path><path d="M14 12h.01"></path><path d="M10 16h.01"></path><path d="M14 16h.01"></path></svg>
<span>Playground</span>
</a>
<a class="flex items-center gap-3 px-4 py-2.5 rounded-md text-[var(--pg-text-secondary)] hover:bg-[var(--pg-secondary)] hover:text-white transition-colors" href="#">
<svg fill="none" height="24" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" viewBox="0 0 24 24" width="24" xmlns="http://www.w3.org/2000/svg"><rect height="18" rx="2" width="18" x="3" y="3"></rect><path d="M7 8h10"></path><path d="M7 12h10"></path><path d="M7 16h5"></path></svg>
<span>Templates</span>
</a>
<a class="flex items-center gap-3 px-4 py-2.5 rounded-md text-[var(--pg-text-secondary)] hover:bg-[var(--pg-secondary)] hover:text-white transition-colors" href="#">
<svg fill="none" height="24" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" viewBox="0 0 24 24" width="24" xmlns="http://www.w3.org/2000/svg"><path d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 0 2l-.15.08a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.38a2 2 0 0 0-.73-2.73l-.15-.1a2 2 0 0 1 0-2l.15-.08a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z"></path><circle cx="12" cy="12" r="3"></circle></svg>
<span>Settings</span>
</a>
</nav>
</div>
<button class="w-full flex items-center justify-center gap-2 rounded-md h-12 bg-[var(--pg-primary)] text-white text-base font-semibold hover:bg-[var(--pg-primary-hover)] transition-colors">
<svg fill="none" height="20" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2.5" viewBox="0 0 24 24" width="20" xmlns="http://www.w3.org/2000/svg"><line x1="12" x2="12" y1="5" y2="19"></line><line x1="5" x2="19" y1="12" y2="12"></line></svg>
<span>New Prompt</span>
</button>
</aside>
<main class="flex-1 p-8 overflow-y-auto">
<h1 class="text-4xl font-bold mb-8">Home</h1>
<div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
<div class="bg-[var(--pg-surface)] p-6 rounded-lg border border-[var(--pg-border)] flex items-center gap-4">
<div class="bg-blue-500/10 p-3 rounded-md">
<svg class="text-blue-500" fill="none" height="28" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" viewBox="0 0 24 24" width="28" xmlns="http://www.w3.org/2000/svg"><path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"></path></svg>
</div>
<div>
<p class="text-[var(--pg-text-secondary)] text-sm font-medium">Prompts</p>
<p class="text-3xl font-bold">12</p>
</div>
</div>
<div class="bg-[var(--pg-surface)] p-6 rounded-lg border border-[var(--pg-border)] flex items-center gap-4">
<div class="bg-green-500/10 p-3 rounded-md">
<svg class="text-green-500" fill="none" height="28" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" viewBox="0 0 24 24" width="28" xmlns="http://www.w3.org/2000/svg"><path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"></path><path d="M10 12h.01"></path><path d="M14 12h.01"></path><path d="M10 16h.01"></path><path d="M14 16h.01"></path></svg>
</div>
<div>
<p class="text-[var(--pg-text-secondary)] text-sm font-medium">Playgrounds</p>
<p class="text-3xl font-bold">5</p>
</div>
</div>
<div class="bg-[var(--pg-surface)] p-6 rounded-lg border border-[var(--pg-border)] flex items-center gap-4">
<div class="bg-purple-500/10 p-3 rounded-md">
<svg class="text-purple-500" fill="none" height="28" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" viewBox="0 0 24 24" width="28" xmlns="http://www.w3.org/2000/svg"><rect height="18" rx="2" width="18" x="3" y="3"></rect><path d="M7 8h10"></path><path d="M7 12h10"></path><path d="M7 16h5"></path></svg>
</div>
<div>
<p class="text-[var(--pg-text-secondary)] text-sm font-medium">Templates</p>
<p class="text-3xl font-bold">3</p>
</div>
</div>
</div>
<h2 class="text-2xl font-bold mb-4">Recent Activity</h2>
<div class="bg-[var(--pg-surface)] rounded-lg border border-[var(--pg-border)] overflow-hidden">
<table class="w-full text-left">
<thead class="bg-[#1A1E23] border-b border-[var(--pg-border)]">
<tr>
<th class="px-6 py-4 text-sm font-semibold text-[var(--pg-text-secondary)] uppercase tracking-wider">Name</th>
<th class="px-6 py-4 text-sm font-semibold text-[var(--pg-text-secondary)] uppercase tracking-wider">Type</th>
<th class="px-6 py-4 text-sm font-semibold text-[var(--pg-text-secondary)] uppercase tracking-wider">Created</th>
<th class="px-6 py-4 text-sm font-semibold text-[var(--pg-text-secondary)] uppercase tracking-wider">Status</th>
</tr>
</thead>
<tbody class="divide-y divide-[var(--pg-border)]">
<tr>
<td class="px-6 py-4 whitespace-nowrap text-white font-medium">Prompt 1</td>
<td class="px-6 py-4 whitespace-nowrap">
<span class="px-3 py-1 text-xs font-semibold rounded-full bg-blue-500/20 text-blue-400">Prompt</span>
</td>
<td class="px-6 py-4 whitespace-nowrap text-[var(--pg-text-secondary)]">2023-09-15</td>
<td class="px-6 py-4 whitespace-nowrap">
<span class="inline-flex items-center gap-2 text-green-400">
<span class="h-2 w-2 rounded-full bg-green-400"></span> Active
                </span>
</td>
</tr>
<tr>
<td class="px-6 py-4 whitespace-nowrap text-white font-medium">Playground 1</td>
<td class="px-6 py-4 whitespace-nowrap">
<span class="px-3 py-1 text-xs font-semibold rounded-full bg-green-500/20 text-green-400">Playground</span>
</td>
<td class="px-6 py-4 whitespace-nowrap text-[var(--pg-text-secondary)]">2023-09-14</td>
<td class="px-6 py-4 whitespace-nowrap">
<span class="inline-flex items-center gap-2 text-green-400">
<span class="h-2 w-2 rounded-full bg-green-400"></span> Active
                </span>
</td>
</tr>
<tr>
<td class="px-6 py-4 whitespace-nowrap text-white font-medium">Template 1</td>
<td class="px-6 py-4 whitespace-nowrap">
<span class="px-3 py-1 text-xs font-semibold rounded-full bg-purple-500/20 text-purple-400">Template</span>
</td>
<td class="px-6 py-4 whitespace-nowrap text-[var(--pg-text-secondary)]">2023-09-13</td>
<td class="px-6 py-4 whitespace-nowrap">
<span class="inline-flex items-center gap-2 text-green-400">
<span class="h-2 w-2 rounded-full bg-green-400"></span> Active
                </span>
</td>
</tr>
<tr>
<td class="px-6 py-4 whitespace-nowrap text-white font-medium">Prompt 2</td>
<td class="px-6 py-4 whitespace-nowrap">
<span class="px-3 py-1 text-xs font-semibold rounded-full bg-blue-500/20 text-blue-400">Prompt</span>
</td>
<td class="px-6 py-4 whitespace-nowrap text-[var(--pg-text-secondary)]">2023-09-12</td>
<td class="px-6 py-4 whitespace-nowrap">
<span class="inline-flex items-center gap-2 text-gray-400">
<span class="h-2 w-2 rounded-full bg-gray-400"></span> Inactive
                </span>
</td>
</tr>
<tr>
<td class="px-6 py-4 whitespace-nowrap text-white font-medium">Playground 2</td>
<td class="px-6 py-4 whitespace-nowrap">
<span class="px-3 py-1 text-xs font-semibold rounded-full bg-green-500/20 text-green-400">Playground</span>
</td>
<td class="px-6 py-4 whitespace-nowrap text-[var(--pg-text-secondary)]">2023-09-11</td>
<td class="px-6 py-4 whitespace-nowrap">
<span class="inline-flex items-center gap-2 text-green-400">
<span class="h-2 w-2 rounded-full bg-green-400"></span> Active
                </span>
</td>
</tr>
</tbody>
</table>
</div>
</main>
</div>

</body></html>

# Code 2

<!DOCTYPE html>
<html lang="en"><head>
<meta charset="utf-8"/>
<link crossorigin="" href="https://fonts.gstatic.com/" rel="preconnect"/>
<link as="style" href="https://fonts.googleapis.com/css2?display=swap&amp;family=Inter%3Awght%40400%3B500%3B700%3B900&amp;family=Noto+Sans%3Awght%40400%3B500%3B700%3B900" onload="this.rel='stylesheet'" rel="stylesheet"/>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&amp;display=swap" rel="stylesheet"/>
<link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet"/>
<script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
<style type="text/tailwindcss">
      :root {
        --primary-50: #eef7ff;
        --primary-100: #d9ecff;
        --primary-200: #bce0ff;
        --primary-300: #8fd0ff;
        --primary-400: #5cb7ff;
        --primary-500: #3398ff;
        --primary-600: #0d7ff2;
        --primary-700: #0068d3;
        --primary-800: #0056b3;
        --primary-900: #024387;
        --secondary-50: #f0f3f8;
        --secondary-100: #e1e7f1;
        --secondary-200: #c9d4e5;
        --secondary-300: #a4b8d2;
        --secondary-400: #7b95ba;
        --secondary-500: #5f7a9f;
        --secondary-600: #4e6484;
        --secondary-700: #42536e;
        --secondary-800: #39465a;
        --secondary-900: #313b4c;
        --neutral-50: #f7f7f7;
        --neutral-100: #eeeeee;
        --neutral-200: #e0e0e0;
        --neutral-300: #cccccc;
        --neutral-400: #a6a6a6;
        --neutral-500: #8a8a8a;
        --neutral-600: #666666;
        --neutral-700: #525252;
        --neutral-800: #3d3d3d;
        --neutral-900: #2e2e2e;
        --background: #111418;
      }
      body {
        font-family: "Inter", sans-serif;
        background-color: var(--background);
      }
    </style>
<title>PromptWizard - Configure Optimization</title>
<link href="data:image/x-icon;base64," rel="icon" type="image/x-icon"/>
</head>
<body>
<div class="relative flex size-full min-h-screen flex-col bg-[var(--background)] text-white group/design-root overflow-x-hidden">
<div class="layout-container flex h-full grow flex-col">
<header class="flex items-center justify-between whitespace-nowrap border-b border-solid border-[var(--secondary-800)] px-10 py-4">
<div class="flex items-center gap-4">
<svg class="text-[var(--primary-600)]" fill="none" height="32" viewBox="0 0 48 48" width="32" xmlns="http://www.w3.org/2000/svg">
<path clip-rule="evenodd" d="M39.475 21.6262C40.358 21.4363 40.6863 21.5589 40.7581 21.5934C40.7876 21.655 40.8547 21.857 40.8082 22.3336C40.7408 23.0255 40.4502 24.0046 39.8572 25.2301C38.6799 27.6631 36.5085 30.6631 33.5858 33.5858C30.6631 36.5085 27.6632 38.6799 25.2301 39.8572C24.0046 40.4502 23.0255 40.7407 22.3336 40.8082C21.8571 40.8547 21.6551 40.7875 21.5934 40.7581C21.5589 40.6863 21.4363 40.358 21.6262 39.475C21.8562 38.4054 22.4689 36.9657 23.5038 35.2817C24.7575 33.2417 26.5497 30.9744 28.7621 28.762C30.9744 26.5497 33.2417 24.7574 35.2817 23.5037C36.9657 22.4689 38.4054 21.8562 39.475 21.6262ZM4.41189 29.2403L18.7597 43.5881C19.8813 44.7097 21.4027 44.9179 22.7217 44.7893C24.0585 44.659 25.5148 44.1631 26.9723 43.4579C29.9052 42.0387 33.2618 39.5667 36.4142 36.4142C39.5667 33.2618 42.0387 29.9052 43.4579 26.9723C44.1631 25.5148 44.659 24.0585 44.7893 22.7217C44.9179 21.4027 44.7097 19.8813 43.5881 18.7597L29.2403 4.41187C27.8527 3.02428 25.8765 3.02573 24.2861 3.36776C22.6081 3.72863 20.7334 4.58419 18.8396 5.74801C16.4978 7.18716 13.9881 9.18353 11.5858 11.5858C9.18354 13.988 7.18717 16.4978 5.74802 18.8396C4.58421 20.7334 3.72865 22.6081 3.36778 24.2861C3.02574 25.8765 3.02429 27.8527 4.41189 29.2403Z" fill="currentColor" fill-rule="evenodd"></path>
</svg>
<h1 class="text-xl font-bold tracking-tighter">PromptWizard</h1>
</div>
<nav class="flex flex-1 justify-center">
<div class="flex items-center gap-8">
<a class="text-[var(--secondary-200)] hover:text-white transition-colors text-sm font-medium" href="#">Dashboard</a>
<a class="text-white text-sm font-semibold border-b-2 border-[var(--primary-600)] pb-1" href="#">Prompts</a>
<a class="text-[var(--secondary-200)] hover:text-white transition-colors text-sm font-medium" href="#">Playground</a>
<a class="text-[var(--secondary-200)] hover:text-white transition-colors text-sm font-medium" href="#">Docs</a>
</div>
</nav>
<div class="flex items-center gap-4">
<button class="flex h-10 w-10 items-center justify-center rounded-full bg-[var(--secondary-900)] text-[var(--secondary-200)] hover:bg-[var(--secondary-800)] hover:text-white transition-colors">
<span class="material-icons text-xl">help_outline</span>
</button>
<div class="bg-center bg-no-repeat aspect-square bg-cover rounded-full size-10 ring-2 ring-offset-2 ring-offset-[var(--background)] ring-[var(--secondary-700)]" style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuDjSya3E9nLYyi5z1DtvpgowZae8nZW0iM10sqdKmIyjx0Pw3iVHKgL45koT619FZa7Rf1eByBb_khfkOjMmfx_aC13xqsnCpRPwnnutPUnn0RMgg11vRx8TyqjFrM2JLIWnRxqlWbQEaXqOqBuKhSUuCYi10K-xNET78hsfcCEYRCPYKF4U1OqEqeEYuJL1wj0q-e_Jg6slt0HBYJSH2z_vQvIT5p9ZpRjl6SXyo0wJqd0Q5WVqH2qMGdBdYfGKDbMhGrg1F8DMB2M");'></div>
</div>
</header>
<main class="flex flex-1 justify-center py-12 px-4">
<div class="w-full max-w-2xl space-y-8">
<div class="text-center">
<h2 class="text-4xl font-bold tracking-tighter">Configure Prompt Optimization</h2>
<p class="mt-2 text-[var(--secondary-300)]">Fine-tune the parameters to optimize your prompt.</p>
</div>
<form class="space-y-6">
<div>
<label class="block text-sm font-medium text-[var(--secondary-200)] mb-2" for="prompt-input">Your Prompt</label>
<textarea class="form-textarea w-full resize-y rounded-md bg-[var(--secondary-900)] border border-[var(--secondary-800)] focus:ring-2 focus:ring-[var(--primary-600)] focus:border-[var(--primary-600)] text-white placeholder-[var(--secondary-400)] p-4 min-h-[120px] transition-colors" id="prompt-input" placeholder="Enter your prompt here, for example: 'Create a marketing campaign for a new brand of sustainable sneakers...'"></textarea>
</div>
<div class="grid grid-cols-1 md:grid-cols-2 gap-6">
<div>
<label class="flex items-center text-sm font-medium text-[var(--secondary-200)] mb-2" for="optimization-technique">Optimization Technique
                    <span class="material-icons text-base text-[var(--secondary-400)] ml-1 cursor-help" title="Select the AI technique used to refine your prompt.">info</span></label>
<select class="form-select w-full rounded-md bg-[var(--secondary-900)] border border-[var(--secondary-800)] focus:ring-2 focus:ring-[var(--primary-600)] focus:border-[var(--primary-600)] text-white p-3 transition-colors" id="optimization-technique">
<option value="critique-refine">Critique &amp; Refine</option>
<option value="few-shot-learning">Few-Shot Learning</option>
<option value="chain-of-thought">Chain of Thought</option>
</select>
</div>
<div>
<label class="flex items-center text-sm font-medium text-[var(--secondary-200)] mb-2" for="iteration-count">Iteration Count
                    <span class="material-icons text-base text-[var(--secondary-400)] ml-1 cursor-help" title="Number of times the model will try to improve the prompt.">info</span></label>
<input class="form-input w-full rounded-md bg-[var(--secondary-900)] border border-[var(--secondary-800)] focus:ring-2 focus:ring-[var(--primary-600)] focus:border-[var(--primary-600)] text-white p-3 transition-colors" id="iteration-count" type="number" value="5"/>
</div>
</div>
<div class="grid grid-cols-1 md:grid-cols-2 gap-6">
<div>
<label class="flex items-center text-sm font-medium text-[var(--secondary-200)] mb-2" for="batch-size">Batch Size
                    <span class="material-icons text-base text-[var(--secondary-400)] ml-1 cursor-help" title="Number of prompt variations to generate in each iteration.">info</span></label>
<input class="form-input w-full rounded-md bg-[var(--secondary-900)] border border-[var(--secondary-800)] focus:ring-2 focus:ring-[var(--primary-600)] focus:border-[var(--primary-600)] text-white p-3 transition-colors" id="batch-size" type="number" value="3"/>
</div>
<div>
<label class="flex items-center justify-between text-sm font-medium text-[var(--secondary-200)] mb-2" for="success-threshold"><span class="flex items-center">Success Threshold<span class="material-icons text-base text-[var(--secondary-400)] ml-1 cursor-help" title="The minimum quality score for a prompt to be considered successful.">info</span></span><span class="font-mono text-sm text-white">0.8</span></label>
<input class="form-range h-2 w-full cursor-pointer appearance-none rounded-lg bg-[var(--secondary-800)] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-[var(--secondary-900)] focus:ring-[var(--primary-600)] accent-[var(--primary-600)]" id="success-threshold" max="1" min="0" step="0.05" type="range" value="0.8"/>
</div>
</div>
<div class="flex justify-end pt-4">
<button class="flex items-center justify-center gap-2 rounded-md bg-[var(--primary-600)] hover:bg-[var(--primary-700)] text-white text-sm font-bold py-3 px-6 transition-colors disabled:opacity-50" type="submit">
<span class="material-icons">auto_awesome</span>
                  Start Optimization
                </button>
</div>
</form>
</div>
</main>
</div>
</div>

</body></html>

# Code 3

<!DOCTYPE html>
<html lang="en"><head>
<meta charset="utf-8"/>
<title>PromptWizard - Optimization in Progress</title>
<link href="data:image/x-icon;base64," rel="icon" type="image/x-icon"/>
<link href="https://fonts.googleapis.com" rel="preconnect"/>
<link crossorigin="" href="https://fonts.gstatic.com" rel="preconnect"/>
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&amp;display=swap" rel="stylesheet"/>
<script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
<style type="text/tailwindcss">
      :root {
        --primary-500: #0d7ff2;
        --primary-600: #0b68c5;
        --neutral-800: #1f2937;
        --neutral-700: #374151;
        --neutral-600: #4b5563;
        --neutral-500: #6b7280;
        --neutral-400: #9ca3af;
        --neutral-900: #111827;
      }
      body {
        font-family: "Inter", sans-serif;
      }
    </style>
</head>
<body class="bg-neutral-900 text-white">
<div class="flex min-h-screen flex-col">
<header class="sticky top-0 z-20 border-b border-neutral-800 bg-neutral-900/80 backdrop-blur-sm">
<div class="container mx-auto px-4 sm:px-6 lg:px-8">
<div class="flex h-16 items-center justify-between">
<div class="flex items-center gap-4">
<div class="flex-shrink-0">
<svg class="h-8 w-8 text-[var(--primary-500)]" fill="none" viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg">
<path clip-rule="evenodd" d="M39.475 21.6262C40.358 21.4363 40.6863 21.5589 40.7581 21.5934C40.7876 21.655 40.8547 21.857 40.8082 22.3336C40.7408 23.0255 40.4502 24.0046 39.8572 25.2301C38.6799 27.6631 36.5085 30.6631 33.5858 33.5858C30.6631 36.5085 27.6632 38.6799 25.2301 39.8572C24.0046 40.4502 23.0255 40.7407 22.3336 40.8082C21.8571 40.8547 21.6551 40.7875 21.5934 40.7581C21.5589 40.6863 21.4363 40.358 21.6262 39.475C21.8562 38.4054 22.4689 36.9657 23.5038 35.2817C24.7575 33.2417 26.5497 30.9744 28.7621 28.762C30.9744 26.5497 33.2417 24.7574 35.2817 23.5037C36.9657 22.4689 38.4054 21.8562 39.475 21.6262ZM4.41189 29.2403L18.7597 43.5881C19.8813 44.7097 21.4027 44.9179 22.7217 44.7893C24.0585 44.659 25.5148 44.1631 26.9723 43.4579C29.9052 42.0387 33.2618 39.5667 36.4142 36.4142C39.5667 33.2618 42.0387 29.9052 43.4579 26.9723C44.1631 25.5148 44.659 24.0585 44.7893 22.7217C44.9179 21.4027 44.7097 19.8813 43.5881 18.7597L29.2403 4.41187C27.8527 3.02428 25.8765 3.02573 24.2861 3.36776C22.6081 3.72863 20.7334 4.58419 18.8396 5.74801C16.4978 7.18716 13.9881 9.18353 11.5858 11.5858C9.18354 13.988 7.18717 16.4978 5.74802 18.8396C4.58421 20.7334 3.72865 22.6081 3.36778 24.2861C3.02574 25.8765 3.02429 27.8527 4.41189 29.2403Z" fill="currentColor" fill-rule="evenodd"></path>
</svg>
</div>
<h1 class="text-xl font-bold">PromptWizard</h1>
</div>
<div class="flex items-center gap-6">
<nav class="hidden md:flex items-center gap-6 text-sm font-medium">
<a class="text-neutral-400 hover:text-white transition-colors" href="#">Dashboard</a>
<a class="text-white" href="#">Prompts</a>
<a class="text-neutral-400 hover:text-white transition-colors" href="#">Playground</a>
<a class="text-neutral-400 hover:text-white transition-colors" href="#">Docs</a>
</nav>
<div class="flex items-center gap-4">
<button class="hidden sm:flex items-center justify-center rounded-md h-10 px-4 bg-neutral-800 text-sm font-semibold hover:bg-neutral-700 transition-colors"> New Prompt </button>
<div class="w-10 h-10 rounded-full bg-cover bg-center" style='background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuB_X0rF_EjUb_yE8Ho3vVv_uHX95ObVN-BlUz7JA3PrrWk3f28mxNwyWRsAYKB-BdF9taJKRINlcK5IWA9ochnqE4dktp8vk_DSzr0bnUCiBitnJqfCuVkwoDLkxlNnXEZvvkYo1LSzl6ixHwx2b1uAPi5zBnEKY-fDXnViZWJnpPCT1GrlFHRW7lYcedS0bxM4Y4HmMzQplzaR74asQcCxtRdelYNKwuU1t8PeSYBRzZeCfO3i015K62Qf7G5vMitf87k1kLwtNw68");'></div>
</div>
</div>
</div>
</div>
</header>
<main class="flex-1">
<div class="container mx-auto px-4 sm:px-6 lg:px-8 py-8 md:py-12">
<div class="max-w-4xl mx-auto">
<div class="mb-8">
<nav class="flex items-center text-sm font-medium text-neutral-400">
<a class="hover:text-white transition-colors" href="#">Prompts</a>
<span class="mx-2">/</span>
<span class="text-white">Optimize</span>
</nav>
<h2 class="mt-2 text-3xl md:text-4xl font-extrabold tracking-tight">Optimizing Prompt</h2>
<p class="mt-2 text-neutral-400">Track the progress of your prompt optimization job in real-time.</p>
</div>
<div class="bg-neutral-800/50 rounded-lg p-6 md:p-8 space-y-8">
<div>
<div class="flex justify-between items-center mb-2">
<h3 class="text-lg font-semibold">Overall Progress</h3>
<span class="text-lg font-bold text-[var(--primary-500)]">60%</span>
</div>
<div class="w-full bg-neutral-700 rounded-full h-2.5">
<div class="bg-[var(--primary-500)] h-2.5 rounded-full" style="width: 60%; transition: width 0.5s ease-in-out;"></div>
</div>
</div>
<div>
<h3 class="text-lg font-semibold mb-2">Current Stage</h3>
<div class="flex items-center gap-3">
<div class="w-4 h-4 rounded-full bg-[var(--primary-500)] animate-pulse"></div>
<p class="text-lg font-medium text-neutral-200">Testing Performance</p>
</div>
<div class="mt-4 text-sm text-neutral-400 flex flex-col space-y-2">
<div class="flex items-center gap-2 text-green-400">
<svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
<path clip-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" fill-rule="evenodd"></path>
</svg>
<span>Initializing</span>
</div>
<div class="flex items-center gap-2 text-green-400">
<svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
<path clip-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" fill-rule="evenodd"></path>
</svg>
<span>Generating Variations</span>
</div>
<div class="flex items-center gap-2 text-neutral-200">
<svg class="w-4 h-4 animate-spin text-[var(--primary-500)]" fill="none" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
<circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
<path class="opacity-75" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" fill="currentColor"></path>
</svg>
<span>Testing Performance</span>
</div>
</div>
</div>
<div>
<h3 class="text-lg font-semibold mb-3">Live Metrics</h3>
<div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
<div class="bg-neutral-700/50 rounded-lg p-4">
<p class="text-sm font-medium text-neutral-400">Success Rate</p>
<p class="text-3xl font-bold text-green-400 mt-1">85%</p>
</div>
<div class="bg-neutral-700/50 rounded-lg p-4">
<p class="text-sm font-medium text-neutral-400">Improvement</p>
<p class="text-3xl font-bold text-cyan-400 mt-1">+20%</p>
</div>
</div>
</div>
<div>
<h3 class="text-lg font-semibold mb-3">Estimated Time Remaining</h3>
<div class="grid grid-cols-3 gap-4 text-center">
<div>
<div class="bg-neutral-700/50 rounded-lg py-3 px-2">
<p class="text-4xl font-bold tracking-tight">00</p>
</div>
<p class="text-xs text-neutral-400 mt-2">Hours</p>
</div>
<div>
<div class="bg-neutral-700/50 rounded-lg py-3 px-2">
<p class="text-4xl font-bold tracking-tight">15</p>
</div>
<p class="text-xs text-neutral-400 mt-2">Minutes</p>
</div>
<div>
<div class="bg-neutral-700/50 rounded-lg py-3 px-2">
<p class="text-4xl font-bold tracking-tight">30</p>
</div>
<p class="text-xs text-neutral-400 mt-2">Seconds</p>
</div>
</div>
</div>
</div>
<div class="mt-8 flex justify-end">
<button class="flex items-center justify-center rounded-md h-10 px-5 bg-red-600/80 text-sm font-semibold hover:bg-red-600 transition-colors"> Cancel Optimization </button>
</div>
</div>
</div>
</main>
</div>

</body></html>

# Code 4

<!DOCTYPE html>
<html lang="en"><head>
<meta charset="utf-8"/>
<title>PromptWizard Optimization Results</title>
<link crossorigin="" href="https://fonts.gstatic.com/" rel="preconnect"/>
<link as="style" href="https://fonts.googleapis.com/css2?display=swap&amp;family=Inter%3Awght%40400%3B500%3B600%3B700%3B900&amp;family=Noto+Sans%3Awght%40400%3B500%3B700%3B900" onload="this.rel='stylesheet'" rel="stylesheet"/>
<script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
<style type="text/tailwindcss">
      :root {
        --primary-color: #0d7ff2;
        --background-color: #111418;
        --card-background: #181c22;
        --text-primary: #ffffff;
        --text-secondary: #a0aec0;
        --border-color: #2d3748;
        --success-color: #38a169;
        --danger-color: #e53e3e;
      }
      body {
        font-family: 'Inter', "Noto Sans", sans-serif;
      }
    </style>
</head>
<body class="bg-[var(--background-color)] text-[var(--text-primary)]">
<div class="flex min-h-screen">
<aside class="w-64 bg-black/10 p-6 flex flex-col justify-between">
<div>
<h1 class="text-2xl font-bold mb-8 text-[var(--primary-color)]">PromptWizard</h1>
<nav class="flex flex-col gap-2">
<a class="flex items-center gap-3 px-4 py-2 rounded-md hover:bg-[var(--card-background)] text-[var(--text-secondary)]" href="#">
<svg fill="currentColor" height="24" viewBox="0 0 256 256" width="24" xmlns="http://www.w3.org/2000/svg"><path d="M218.83,103.77l-80-75.48a1.14,1.14,0,0,1-.11-.11,16,16,0,0,0-21.53,0l-.11.11L37.17,103.77A16,16,0,0,0,32,115.55V208a16,16,0,0,0,16,16H96a16,16,0,0,0,16-16V160h32v48a16,16,0,0,0,16,16h48a16,16,0,0,0,16-16V115.55A16,16,0,0,0,218.83,103.77ZM208,208H160V160a16,16,0,0,0-16-16H112a16,16,0,0,0-16,16v48H48V115.55l.11-.1L128,40l79.9,75.43.11.1Z"></path></svg>
<span>Home</span>
</a>
<a class="flex items-center gap-3 px-4 py-2 rounded-md bg-[var(--primary-color)] text-white" href="#">
<svg fill="currentColor" height="24" viewBox="0 0 256 256" width="24" xmlns="http://www.w3.org/2000/svg"><path d="M213.66,82.34l-56-56A8,8,0,0,0,152,24H56A16,16,0,0,0,40,40V216a16,16,0,0,0,16,16H200a16,16,0,0,0,16-16V88A8,8,0,0,0,213.66,82.34ZM152,88V44l44,44Z"></path></svg>
<span>Prompts</span>
</a>
<a class="flex items-center gap-3 px-4 py-2 rounded-md hover:bg-[var(--card-background)] text-[var(--text-secondary)]" href="#">
<svg fill="currentColor" height="24" viewBox="0 0 256 256" width="24" xmlns="http://www.w3.org/2000/svg"><path d="M216,40H40A16,16,0,0,0,24,56V200a16,16,0,0,0,16,16H216a16,16,0,0,0,16-16V56A16,16,0,0,0,216,40Zm0,160H40V56H216V200ZM80,84A12,12,0,1,1,68,72,12,12,0,0,1,80,84Zm40,0a12,12,0,1,1-12-12A12,12,0,0,1,120,84Z"></path></svg>
<span>Playground</span>
</a>
<a class="flex items-center gap-3 px-4 py-2 rounded-md hover:bg-[var(--card-background)] text-[var(--text-secondary)]" href="#">
<svg fill="currentColor" height="24" viewBox="0 0 256 256" width="24" xmlns="http://www.w3.org/2000/svg"><path d="M213.66,82.34l-56-56A8,8,0,0,0,152,24H56A16,16,0,0,0,40,40V216a16,16,0,0,0,16,16H200a16,16,0,0,0,16-16V88A8,8,0,0,0,213.66,82.34ZM160,51.31,188.69,80H160ZM200,216H56V40h88V88a8,8,0,0,0,8,8h48V216Zm-32-80a8,8,0,0,1-8,8H96a8,8,0,0,1,0-16h64A8,8,0,0,1,168,136Zm0,32a8,8,0,0,1-8,8H96a8,8,0,0,1,0-16h64A8,8,0,0,1,168,168Z"></path></svg>
<span>Docs</span>
</a>
</nav>
</div>
<a class="flex items-center gap-3 px-4 py-2 rounded-md hover:bg-[var(--card-background)] text-[var(--text-secondary)]" href="#">
<svg fill="currentColor" height="24" viewBox="0 0 256 256" width="24" xmlns="http://www.w3.org/2000/svg"><path d="M128,80a48,48,0,1,0,48,48A48.05,48.05,0,0,0,128,80Zm0,80a32,32,0,1,1,32-32A32,32,0,0,1,128,160Zm88-29.84q.06-2.16,0-4.32l14.92-18.64a8,8,0,0,0,1.48-7.06,107.21,107.21,0,0,0-10.88-26.25,8,8,0,0,0-6-3.93l-23.72-2.64q-1.48-1.56-3-3L186,40.54a8,8,0,0,0-3.94-6,107.71,107.71,0,0,0-26.25-10.87,8,8,0,0,0-7.06,1.49L130.16,40Q128,40,125.84,40L107.2,25.11a8,8,0,0,0-7.06-1.48A107.6,107.6,0,0,0,73.89,34.51a8,8,0,0,0-3.93,6L67.32,64.27q-1.56,1.49-3,3L40.54,70a8,8,0,0,0-6,3.94,107.71,107.71,0,0,0-10.87,26.25,8,8,0,0,0,1.49,7.06L40,125.84Q40,128,40,130.16L25.11,148.8a8,8,0,0,0-1.48,7.06,107.21,107.21,0,0,0,10.88,26.25,8,8,0,0,0,6,3.93l23.72,2.64q1.49,1.56,3,3L70,215.46a8,8,0,0,0,3.94,6,107.71,107.71,0,0,0,26.25,10.87,8,8,0,0,0,7.06-1.49L125.84,216q2.16.06,4.32,0l18.64,14.92a8,8,0,0,0,7.06,1.48,107.21,107.21,0,0,0,26.25-10.88,8,8,0,0,0,3.93-6l2.64-23.72q1.56-1.48,3-3L215.46,186a8,8,0,0,0,6-3.94,107.71,107.71,0,0,0,10.87-26.25,8,8,0,0,0-1.49-7.06Zm-16.1-6.5a73.93,73.93,0,0,1,0,8.68,8,8,0,0,0,1.74,5.48l14.19,17.73a91.57,91.57,0,0,1-6.23,15L187,173.11a8,8,0,0,0-5.1,2.64,74.11,74.11,0,0,1-6.14,6.14,8,8,0,0,0-2.64,5.1l-2.51,22.58a91.32,91.32,0,0,1-15,6.23l-17.74-14.19a8,8,0,0,0-5-1.75h-.48a73.93,73.93,0,0,1-8.68,0,8,8,0,0,0-5.48,1.74L100.45,215.8a91.57,91.57,0,0,1-15-6.23L82.89,187a8,8,0,0,0-2.64-5.1,74.11,74.11,0,0,1-6.14-6.14,8,8,0,0,0-5.1-2.64L46.43,170.6a91.32,91.32,0,0,1-6.23-15l14.19-17.74a8,8,0,0,0,1.74-5.48,73.93,73.93,0,0,1,0-8.68,8,8,0,0,0-1.74-5.48L40.2,100.45a91.57,91.57,0,0,1,6.23-15L69,82.89a8,8,0,0,0,5.1-2.64,74.11,74.11,0,0,1,6.14-6.14A8,8,0,0,0,82.89,69L85.4,46.43a91.32,91.32,0,0,1,15-6.23l17.74,14.19a8,8,0,0,0,5.48,1.74,73.93,73.93,0,0,1,8.68,0,8,8,0,0,0,5.48-1.74L155.55,40.2a91.57,91.57,0,0,1,15,6.23L173.11,69a8,8,0,0,0,2.64,5.1,74.11,74.11,0,0,1,6.14,6.14,8,8,0,0,0,5.1,2.64l22.58,2.51a91.32,91.32,0,0,1,6.23,15l-14.19,17.74A8,8,0,0,0,199.87,123.66Z"></path></svg>
<span>Settings</span>
</a>
</aside>
<main class="flex-1 p-8 overflow-y-auto">
<header class="flex justify-between items-center mb-8">
<h2 class="text-4xl font-bold">Prompt Optimization Results</h2>
<div class="flex gap-3">
<button class="flex items-center gap-2 px-4 py-2 bg-[var(--card-background)] hover:bg-gray-700 text-white rounded-md text-sm font-semibold">
<svg fill="currentColor" height="20" viewBox="0 0 256 256" width="20" xmlns="http://www.w3.org/2000/svg"><path d="M224,144v64a8,8,0,0,1-8,8H40a8,8,0,0,1-8-8V144a8,8,0,0,1,16,0v56H208V144a8,8,0,0,1,16,0ZM93.66,125.66a8,8,0,0,0,11.31,1.31L120,112.63V184a8,8,0,0,0,16,0V112.63l14.95,14.34a8,8,0,0,0,11.31-1.31,8,8,0,0,0-1.32-11.32l-28.69-27.5a8,8,0,0,0-10,0L95,114.34A8,8,0,0,0,93.66,125.66Z"></path></svg>
              Export as JSON
            </button>
<button class="flex items-center gap-2 px-4 py-2 bg-[var(--card-background)] hover:bg-gray-700 text-white rounded-md text-sm font-semibold">
<svg fill="currentColor" height="20" viewBox="0 0 256 256" width="20" xmlns="http://www.w3.org/2000/svg"><path d="M224,144v64a8,8,0,0,1-8,8H40a8,8,0,0,1-8-8V144a8,8,0,0,1,16,0v56H208V144a8,8,0,0,1,16,0ZM93.66,125.66a8,8,0,0,0,11.31,1.31L120,112.63V184a8,8,0,0,0,16,0V112.63l14.95,14.34a8,8,0,0,0,11.31-1.31,8,8,0,0,0-1.32-11.32l-28.69-27.5a8,8,0,0,0-10,0L95,114.34A8,8,0,0,0,93.66,125.66Z"></path></svg>
              Export as CSV
            </button>
</div>
</header>
<section class="mb-8">
<h3 class="text-xl font-semibold mb-4">Comparison</h3>
<div class="grid grid-cols-1 md:grid-cols-2 gap-6">
<div class="bg-[var(--card-background)] p-6 rounded-lg border border-[var(--border-color)]">
<h4 class="text-lg font-semibold mb-3">Original Prompt</h4>
<p class="text-[var(--text-secondary)]">Generate a creative story about a magical forest.</p>
</div>
<div class="bg-[var(--card-background)] p-6 rounded-lg border border-[var(--border-color)] relative">
<span class="absolute top-3 right-3 bg-[var(--primary-color)] text-white text-xs font-bold px-2 py-1 rounded-full">OPTIMIZED</span>
<h4 class="text-lg font-semibold mb-3">Optimized Prompt</h4>
<p class="text-[var(--text-secondary)]">Craft a captivating narrative centered around an enchanted woodland, filled with mystical creatures and ancient secrets.</p>
</div>
</div>
</section>
<section class="mb-8">
<h3 class="text-xl font-semibold mb-4">Performance Metrics</h3>
<div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
<div class="bg-[var(--card-background)] p-6 rounded-lg border border-[var(--border-color)]">
<p class="text-[var(--text-secondary)] mb-1">Success Rate</p>
<p class="text-3xl font-bold">85%</p>
<p class="text-[var(--success-color)] font-semibold">+5%</p>
</div>
<div class="bg-[var(--card-background)] p-6 rounded-lg border border-[var(--border-color)]">
<p class="text-[var(--text-secondary)] mb-1">Improvement</p>
<p class="text-3xl font-bold">20%</p>
<p class="text-[var(--success-color)] font-semibold">+10%</p>
</div>
<div class="bg-[var(--card-background)] p-6 rounded-lg border border-[var(--border-color)]">
<p class="text-[var(--text-secondary)] mb-1">Average Response Time</p>
<p class="text-3xl font-bold">2.5s</p>
<p class="text-[var(--danger-color)] font-semibold">-0.5s</p>
</div>
</div>
</section>
<section class="mb-8 bg-[var(--card-background)] p-6 rounded-lg border border-[var(--border-color)]">
<div class="flex justify-between items-start mb-4">
<div>
<h3 class="text-xl font-semibold">Success Rate Over Iterations</h3>
<p class="text-[var(--text-secondary)] text-sm">Last 7 Iterations</p>
</div>
<div class="text-right">
<p class="text-3xl font-bold">85%</p>
<p class="text-[var(--success-color)] font-semibold">+5%</p>
</div>
</div>
<div class="h-60">
<svg fill="none" height="100%" preserveAspectRatio="none" viewBox="0 0 472 150" width="100%" xmlns="http://www.w3.org/2000/svg">
<path d="M0 109C18.1538 109 18.1538 21 36.3077 21C54.4615 21 54.4615 41 72.6154 41C90.7692 41 90.7692 93 108.923 93C127.077 93 127.077 33 145.231 33C163.385 33 163.385 101 181.538 101C199.692 101 199.692 61 217.846 61C236 61 236 45 254.154 45C272.308 45 272.308 121 290.462 121C308.615 121 308.615 149 326.769 149C344.923 149 344.923 1 363.077 1C381.231 1 381.231 81 399.385 81C417.538 81 417.538 129 435.692 129C453.846 129 453.846 25 472 25" stroke="var(--primary-color)" stroke-linecap="round" stroke-width="3"></path>
<path d="M0 109C18.1538 109 18.1538 21 36.3077 21C54.4615 21 54.4615 41 72.6154 41C90.7692 41 90.7692 93 108.923 93C127.077 93 127.077 33 145.231 33C163.385 33 163.385 101 181.538 101C199.692 101 199.692 61 217.846 61C236 61 236 45 254.154 45C272.308 45 272.308 121 290.462 121C308.615 121 308.615 149 326.769 149C344.923 149 344.923 1 363.077 1C381.231 1 381.231 81 399.385 81C417.538 81 417.538 129 435.692 129C453.846 129 453.846 25 472 25V149H0V109Z" fill="url(#paint0_linear_1_1)"></path>
<defs>
<linearGradient gradientUnits="userSpaceOnUse" id="paint0_linear_1_1" x1="236" x2="236" y1="1" y2="149">
<stop stop-color="var(--primary-color)" stop-opacity="0.3"></stop>
<stop offset="1" stop-color="var(--primary-color)" stop-opacity="0"></stop>
</linearGradient>
</defs>
</svg>
</div>
<div class="flex justify-between text-xs text-[var(--text-secondary)] mt-2">
<span>Iteration 1</span>
<span>Iteration 2</span>
<span>Iteration 3</span>
<span>Iteration 4</span>
<span>Iteration 5</span>
<span>Iteration 6</span>
<span>Iteration 7</span>
</div>
</section>
<section>
<h3 class="text-xl font-semibold mb-4">Iteration History</h3>
<div class="relative pl-8 border-l-2 border-dashed border-[var(--border-color)]">
<div class="mb-8">
<div class="absolute -left-4 top-1.5 w-7 h-7 bg-[var(--primary-color)] rounded-full flex items-center justify-center ring-4 ring-[var(--background-color)]">
<svg fill="currentColor" height="16" viewBox="0 0 256 256" width="16" xmlns="http://www.w3.org/2000/svg"><path d="M229.66,85.66l-48,48a8,8,0,0,1-11.32-11.32L212.69,80H152a64,64,0,0,0-64,64v8a8,8,0,0,1-16,0v-8a80,80,0,0,1,80-80h60.69L170.34,37.66a8,8,0,0,1,11.32-11.32l48,48A8,8,0,0,1,229.66,85.66Z"></path></svg>
</div>
<h4 class="font-semibold text-lg">Initial Prompt</h4>
<p class="text-[var(--text-secondary)]">Generate a creative story about a magical forest.</p>
</div>
<div class="mb-8">
<div class="absolute -left-4 top-1.5 w-7 h-7 bg-[var(--primary-color)] rounded-full flex items-center justify-center ring-4 ring-[var(--background-color)]">
<span class="font-bold text-xs">1</span>
</div>
<h4 class="font-semibold text-lg">Iteration 1: Refine Language</h4>
<p class="text-[var(--text-secondary)]">Craft a story about a magical forest.</p>
</div>
<div class="mb-8">
<div class="absolute -left-4 top-1.5 w-7 h-7 bg-[var(--primary-color)] rounded-full flex items-center justify-center ring-4 ring-[var(--background-color)]">
<span class="font-bold text-xs">2</span>
</div>
<h4 class="font-semibold text-lg">Iteration 2: Add Specificity</h4>
<p class="text-[var(--text-secondary)]">Craft a story about an enchanted woodland, filled with mystical creatures.</p>
</div>
<div class="mb-8">
<div class="absolute -left-4 top-1.5 w-7 h-7 bg-[var(--primary-color)] rounded-full flex items-center justify-center ring-4 ring-[var(--background-color)]">
<span class="font-bold text-xs">3</span>
</div>
<h4 class="font-semibold text-lg">Iteration 3: Enhance Creativity</h4>
<p class="text-[var(--text-secondary)]">Craft a captivating narrative centered around an enchanted woodland, filled with mystical creatures and ancient secrets.</p>
</div>
<div>
<div class="absolute -left-4 top-1.5 w-7 h-7 bg-green-500 rounded-full flex items-center justify-center ring-4 ring-[var(--background-color)]">
<svg fill="currentColor" height="16" viewBox="0 0 256 256" width="16" xmlns="http://www.w3.org/2000/svg"><path d="M229.66,77.66l-128,128a8,8,0,0,1-11.32,0l-56-56a8,8,0,0,1,11.32-11.32L96,188.69,218.34,66.34a8,8,0,0,1,11.32,11.32Z"></path></svg>
</div>
<h4 class="font-semibold text-lg">Final Optimized Prompt</h4>
<p class="text-[var(--text-secondary)]">Craft a captivating narrative centered around an enchanted woodland, filled with mystical creatures and ancient secrets.</p>
</div>
</div>
</section>
</main>
</div>

</body></html>