# Python
__pycache__/
*.pyc
*.pyo
*.pyd
.Python
env/
venv/
.venv/
pip-log.txt
pip-delete-this-directory.txt
.tox/
.coverage
.pytest_cache/
htmlcov/
.mypy_cache/
.ruff_cache/

# Node.js
node_modules/
dist/
build/
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*
.npm/
.yarn/

# IDEs
.vscode/
.idea/

# Operating System
.DS_Store
Thumbs.db

# Logs
*.log

# Docker
*.env
!docker-compose.yml
!Dockerfile

# Other
temp/
uploads/