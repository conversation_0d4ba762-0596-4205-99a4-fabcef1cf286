# Web Framework
fastapi==0.104.1
uvicorn[standard]==0.24.0

# Database
sqlalchemy==2.0.23
alembic==1.12.1
psycopg2-binary==2.9.9

# Redis & Caching
redis==5.0.1
hiredis==2.2.3

# Background Tasks
celery==5.3.4
flower==2.0.1

# Security & Authentication
passlib[bcrypt]==1.7.4
python-jose[cryptography]==3.3.0
python-multipart==0.0.6

# Data Validation & Serialization
pydantic==2.5.0
pydantic-settings==2.1.0

# HTTP Client
httpx==0.25.2

# System Monitoring
psutil==5.9.6

# Development & Testing
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
factory-boy==3.3.0
faker==20.1.0

# Code Quality
ruff==0.1.6
black==23.11.0
isort==5.12.0
mypy==1.7.1

# Environment & Configuration
python-dotenv==1.0.0

# File Handling
aiofiles==23.2.1
pillow==10.1.0

# Logging
structlog==23.2.0

# Date & Time
python-dateutil==2.8.2

# JSON Handling
orjson==3.9.10

# CORS & Middleware
fastapi-cors==0.0.6

# Rate Limiting
slowapi==0.1.9

# Health Checks
fastapi-health==0.4.0

# Documentation
mkdocs==1.5.3
mkdocs-material==9.4.8
