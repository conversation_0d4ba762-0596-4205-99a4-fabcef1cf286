## Description

*A clear and concise description of the "what" and "why" behind this change. What problem does it solve? What is the goal?*

### **Related Issue**

*This PR addresses the following GitHub Issue. This is mandatory.*

**Fixes:** # (issue number)

## Type of Change

*Please check the box that best describes the nature of this change.*

- [ ] **Bug Fix**
- [ ] **New Feature**
- [ ] **Breaking Change**
- [ ] **Documentation**
- [ ] **CI/CD**

## How Has This Been Tested?

*A clear description of the tests that you ran to verify your changes.*

- [ ] **Unit Tests:** New and existing unit tests pass locally with my changes.
- [ ] **Integration Tests:** New and existing integration tests pass locally.
- [ ] **End-to-End Tests:** E2E tests for the affected user flows have been added/updated and pass.

## Final Checklist for the Author

*This is your personal quality gate.*

- [ ] My code follows the style guidelines and architectural rules of this project.
- [ ] I have performed a self-review of my own code.
- [ ] I have made corresponding changes to the documentation (e.g., `README.md`, `decisions.md`).
- [ ] My changes generate no new warnings from the linter.