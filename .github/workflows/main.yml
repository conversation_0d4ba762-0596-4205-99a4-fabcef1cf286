name: CI/CD Pipeline

on:
  push:
    branches:
      - main
  pull_request:
    branches:
      - main

jobs:
  build-and-test:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v5
      with:
        python-version: '3.12'

    - name: Install Python dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt

    - name: Lint Python code with Ruff
      run: ruff check .

    - name: Run Python tests with Pytest
      run: pytest

    - name: Set up Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '22'

    - name: Install Node.js dependencies
      run: npm install

    - name: Lint JavaScript code with ESLint
      run: npm run lint

    - name: Build Frontend
      run: npm run build --prefix src

    - name: Run Frontend tests with Vitest
      run: npm test --prefix src # Assuming vitest is configured as 'test' script in src/package.json

  deploy:
    needs: build-and-test
    if: github.ref == 'refs/heads/main'
    runs-on: ubuntu-latest
    environment: production # Or staging, depending on your setup

    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Deploy to production (Placeholder)
      run: echo "Deployment logic goes here (e.g., Docker build and push, cloud deployment)"